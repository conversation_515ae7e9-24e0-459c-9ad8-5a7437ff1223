{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { DashboardOutlined, ProjectOutlined, UserOutlined, BookOutlined, RobotOutlined, SettingOutlined, MenuFoldOutlined, MenuUnfoldOutlined, LogoutOutlined, BellOutlined, FolderOutlined, DatabaseOutlined, ControlOutlined, EyeOutlined } from '@ant-design/icons';\nimport { projectAPI } from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取当前项目ID（如果在项目页面中）\n  const getProjectId = () => {\n    const pathParts = location.pathname.split('/');\n    if (pathParts[1] === 'projects' && pathParts[2]) {\n      return pathParts[2];\n    }\n    return null;\n  };\n  const projectId = getProjectId();\n\n  // 加载项目列表\n  useEffect(() => {\n    loadProjects();\n  }, []);\n\n  // 监听路径变化，如果在项目页面则重新加载项目列表\n  useEffect(() => {\n    if (location.pathname.includes('/projects')) {\n      loadProjects();\n    }\n  }, [location.pathname]);\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      const response = await projectAPI.getProjects();\n      const projectsData = response.data.projects || [];\n      setProjects(projectsData);\n    } catch (error) {\n      console.error('加载项目列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 构建项目子菜单\n  const buildProjectSubMenu = project => [{\n    key: `/projects/${project.id}`,\n    icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 13\n    }, this),\n    label: '项目概览'\n  }, {\n    key: `/projects/${project.id}/volumes`,\n    icon: /*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this),\n    label: '卷宗管理'\n  }, {\n    key: `/projects/${project.id}/content`,\n    icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this),\n    label: '内容管理'\n  }, {\n    key: `/projects/${project.id}/settings`,\n    icon: /*#__PURE__*/_jsxDEV(ControlOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 13\n    }, this),\n    label: '设定管理'\n  }, {\n    key: `/projects/${project.id}/tools`,\n    icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 13\n    }, this),\n    label: '工具',\n    children: [{\n      key: `/ai-assistant?project=${project.id}`,\n      icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 17\n      }, this),\n      label: 'Agent-AI助手'\n    }, {\n      key: `/ai-test?project=${project.id}`,\n      icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 17\n      }, this),\n      label: 'Agent-AI测试'\n    }, {\n      key: `/settings?project=${project.id}`,\n      icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this),\n      label: '系统设置'\n    }]\n  }];\n\n  // 主菜单项\n  const mainMenuItems = [{\n    key: '/',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 13\n    }, this),\n    label: '仪表盘'\n  }, {\n    key: 'project-management',\n    icon: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 13\n    }, this),\n    label: '项目管理',\n    children: [{\n      key: '/projects',\n      icon: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 17\n      }, this),\n      label: '项目列表'\n    }, ...projects.map(project => ({\n      key: `project-${project.id}`,\n      icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 17\n      }, this),\n      label: project.name,\n      children: buildProjectSubMenu(project)\n    }))]\n  }];\n\n  // 获取当前选中的菜单key\n  const getSelectedKeys = () => {\n    const pathname = location.pathname;\n\n    // 如果是项目相关页面，返回具体的路径\n    if (pathname.startsWith('/projects/')) {\n      return [pathname];\n    }\n\n    // 其他页面直接返回路径\n    return [pathname];\n  };\n\n  // 获取默认展开的菜单key\n  const getDefaultOpenKeys = () => {\n    const pathname = location.pathname;\n    const openKeys = ['project-management'];\n\n    // 如果在项目页面，展开对应的项目菜单\n    if (pathname.startsWith('/projects/') && projectId) {\n      openKeys.push(`project-${projectId}`);\n    }\n    return openKeys;\n  };\n\n  // 用户菜单\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 13\n    }, this),\n    label: '个人资料'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 13\n    }, this),\n    label: '偏好设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 13\n    }, this),\n    label: '退出登录'\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    // 如果是项目管理或项目分组的key，不进行导航\n    if (key === 'project-management' || key.startsWith('project-')) {\n      return;\n    }\n    navigate(key);\n  };\n  const handleUserMenuClick = ({\n    key\n  }) => {\n    switch (key) {\n      case 'profile':\n        navigate('/profile');\n        break;\n      case 'settings':\n        navigate('/settings');\n        break;\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      default:\n        break;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    className: \"layout-container\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      className: \"layout-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 58\n            }, this),\n            onClick: () => setCollapsed(!collapsed),\n            style: {\n              marginRight: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              color: '#1890ff',\n              fontSize: '20px',\n              fontWeight: 'bold'\n            },\n            children: \"NovelCraft\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 39\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems,\n              onClick: handleUserMenuClick\n            },\n            placement: \"bottomRight\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u7528\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      className: \"layout-content\",\n      children: [/*#__PURE__*/_jsxDEV(Sider, {\n        className: \"layout-sider\",\n        collapsed: collapsed,\n        width: 240,\n        collapsedWidth: 80,\n        theme: \"light\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '100%',\n            overflowY: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(Menu, {\n            mode: \"inline\",\n            selectedKeys: getSelectedKeys(),\n            items: mainMenuItems,\n            onClick: handleMenuClick,\n            style: {\n              borderRight: 0\n            },\n            defaultOpenKeys: getDefaultOpenKeys(),\n            openKeys: getDefaultOpenKeys()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        className: \"layout-main\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"FHB6VRdUET1+uwnx3m5zaJZTnb0=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "AntLayout", "<PERSON><PERSON>", "Avatar", "Dropdown", "<PERSON><PERSON>", "Space", "useNavigate", "useLocation", "DashboardOutlined", "ProjectOutlined", "UserOutlined", "BookOutlined", "RobotOutlined", "SettingOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "LogoutOutlined", "BellOutlined", "FolderOutlined", "DatabaseOutlined", "ControlOutlined", "EyeOutlined", "projectAPI", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "children", "_s", "collapsed", "setCollapsed", "projects", "setProjects", "loading", "setLoading", "navigate", "location", "getProjectId", "pathParts", "pathname", "split", "projectId", "loadProjects", "includes", "response", "getProjects", "projectsData", "data", "error", "console", "buildProjectSubMenu", "project", "key", "id", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "mainMenuItems", "map", "name", "getSelectedKeys", "startsWith", "getDefaultOpenKeys", "openKeys", "push", "userMenuItems", "type", "handleMenuClick", "handleUserMenuClick", "log", "className", "style", "display", "alignItems", "justifyContent", "onClick", "marginRight", "margin", "color", "fontSize", "fontWeight", "menu", "items", "placement", "cursor", "width", "collapsedWidth", "theme", "height", "overflowY", "mode", "<PERSON><PERSON><PERSON><PERSON>", "borderRight", "defaultOpenKeys", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/components/Layout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  DashboardOutlined,\n  ProjectOutlined,\n  UserOutlined,\n  BookOutlined,\n  RobotOutlined,\n  SettingOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  LogoutOutlined,\n  BellOutlined,\n  FolderOutlined,\n  DatabaseOutlined,\n  ControlOutlined,\n  EyeOutlined\n} from '@ant-design/icons';\nimport { projectAPI } from '../utils/api';\n\nconst { Header, Sider, Content } = AntLayout;\n\nconst Layout = ({ children }) => {\n  const [collapsed, setCollapsed] = useState(false);\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取当前项目ID（如果在项目页面中）\n  const getProjectId = () => {\n    const pathParts = location.pathname.split('/');\n    if (pathParts[1] === 'projects' && pathParts[2]) {\n      return pathParts[2];\n    }\n    return null;\n  };\n\n  const projectId = getProjectId();\n\n  // 加载项目列表\n  useEffect(() => {\n    loadProjects();\n  }, []);\n\n  // 监听路径变化，如果在项目页面则重新加载项目列表\n  useEffect(() => {\n    if (location.pathname.includes('/projects')) {\n      loadProjects();\n    }\n  }, [location.pathname]);\n\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      const response = await projectAPI.getProjects();\n      const projectsData = response.data.projects || [];\n\n      setProjects(projectsData);\n    } catch (error) {\n      console.error('加载项目列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 构建项目子菜单\n  const buildProjectSubMenu = (project) => [\n    {\n      key: `/projects/${project.id}`,\n      icon: <EyeOutlined />,\n      label: '项目概览',\n    },\n    {\n      key: `/projects/${project.id}/volumes`,\n      icon: <FolderOutlined />,\n      label: '卷宗管理',\n    },\n    {\n      key: `/projects/${project.id}/content`,\n      icon: <DatabaseOutlined />,\n      label: '内容管理',\n    },\n    {\n      key: `/projects/${project.id}/settings`,\n      icon: <ControlOutlined />,\n      label: '设定管理',\n    },\n    {\n      key: `/projects/${project.id}/tools`,\n      icon: <RobotOutlined />,\n      label: '工具',\n      children: [\n        {\n          key: `/ai-assistant?project=${project.id}`,\n          icon: <RobotOutlined />,\n          label: 'Agent-AI助手',\n        },\n        {\n          key: `/ai-test?project=${project.id}`,\n          icon: <RobotOutlined />,\n          label: 'Agent-AI测试',\n        },\n        {\n          key: `/settings?project=${project.id}`,\n          icon: <SettingOutlined />,\n          label: '系统设置',\n        },\n      ],\n    },\n  ];\n\n  // 主菜单项\n  const mainMenuItems = [\n    {\n      key: '/',\n      icon: <DashboardOutlined />,\n      label: '仪表盘',\n    },\n    {\n      key: 'project-management',\n      icon: <ProjectOutlined />,\n      label: '项目管理',\n      children: [\n        {\n          key: '/projects',\n          icon: <ProjectOutlined />,\n          label: '项目列表',\n        },\n        ...projects.map(project => ({\n          key: `project-${project.id}`,\n          icon: <BookOutlined />,\n          label: project.name,\n          children: buildProjectSubMenu(project),\n        })),\n      ],\n    },\n  ];\n\n\n\n  // 获取当前选中的菜单key\n  const getSelectedKeys = () => {\n    const pathname = location.pathname;\n\n    // 如果是项目相关页面，返回具体的路径\n    if (pathname.startsWith('/projects/')) {\n      return [pathname];\n    }\n\n    // 其他页面直接返回路径\n    return [pathname];\n  };\n\n  // 获取默认展开的菜单key\n  const getDefaultOpenKeys = () => {\n    const pathname = location.pathname;\n    const openKeys = ['project-management'];\n\n    // 如果在项目页面，展开对应的项目菜单\n    if (pathname.startsWith('/projects/') && projectId) {\n      openKeys.push(`project-${projectId}`);\n    }\n\n    return openKeys;\n  };\n\n  // 用户菜单\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '偏好设置',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n    },\n  ];\n\n  const handleMenuClick = ({ key }) => {\n    // 如果是项目管理或项目分组的key，不进行导航\n    if (key === 'project-management' || key.startsWith('project-')) {\n      return;\n    }\n    navigate(key);\n  };\n\n  const handleUserMenuClick = ({ key }) => {\n    switch (key) {\n      case 'profile':\n        navigate('/profile');\n        break;\n      case 'settings':\n        navigate('/settings');\n        break;\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      default:\n        break;\n    }\n  };\n\n  return (\n    <AntLayout className=\"layout-container\">\n      <Header className=\"layout-header\">\n        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <div style={{ display: 'flex', alignItems: 'center' }}>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={() => setCollapsed(!collapsed)}\n              style={{ marginRight: 16 }}\n            />\n            <h1 style={{ margin: 0, color: '#1890ff', fontSize: '20px', fontWeight: 'bold' }}>\n              NovelCraft\n            </h1>\n          </div>\n\n          <Space>\n            <Button type=\"text\" icon={<BellOutlined />} />\n            <Dropdown\n              menu={{\n                items: userMenuItems,\n                onClick: handleUserMenuClick,\n              }}\n              placement=\"bottomRight\"\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <span>用户</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </div>\n      </Header>\n\n      <AntLayout className=\"layout-content\">\n        <Sider\n          className=\"layout-sider\"\n          collapsed={collapsed}\n          width={240}\n          collapsedWidth={80}\n          theme=\"light\"\n        >\n          <div style={{ height: '100%', overflowY: 'auto' }}>\n            {/* 主菜单 */}\n            <Menu\n              mode=\"inline\"\n              selectedKeys={getSelectedKeys()}\n              items={mainMenuItems}\n              onClick={handleMenuClick}\n              style={{ borderRight: 0 }}\n              defaultOpenKeys={getDefaultOpenKeys()}\n              openKeys={getDefaultOpenKeys()}\n            />\n          </div>\n        </Sider>\n\n        <Content className=\"layout-main\">\n          {children}\n        </Content>\n      </AntLayout>\n    </AntLayout>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,IAAIC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AACjF,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,eAAe,EACfC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,gBAAgB,EAChBC,eAAe,EACfC,WAAW,QACN,mBAAmB;AAC1B,SAASC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAG3B,SAAS;AAE5C,MAAMD,MAAM,GAAGA,CAAC;EAAE6B;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMuC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM+B,QAAQ,GAAG9B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM+B,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAGF,QAAQ,CAACG,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC;IAC9C,IAAIF,SAAS,CAAC,CAAC,CAAC,KAAK,UAAU,IAAIA,SAAS,CAAC,CAAC,CAAC,EAAE;MAC/C,OAAOA,SAAS,CAAC,CAAC,CAAC;IACrB;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMG,SAAS,GAAGJ,YAAY,CAAC,CAAC;;EAEhC;EACAxC,SAAS,CAAC,MAAM;IACd6C,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7C,SAAS,CAAC,MAAM;IACd,IAAIuC,QAAQ,CAACG,QAAQ,CAACI,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC3CD,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACN,QAAQ,CAACG,QAAQ,CAAC,CAAC;EAEvB,MAAMG,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BR,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMvB,UAAU,CAACwB,WAAW,CAAC,CAAC;MAC/C,MAAMC,YAAY,GAAGF,QAAQ,CAACG,IAAI,CAAChB,QAAQ,IAAI,EAAE;MAEjDC,WAAW,CAACc,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgB,mBAAmB,GAAIC,OAAO,IAAK,CACvC;IACEC,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,EAAE;IAC9BC,IAAI,eAAE/B,OAAA,CAACH,WAAW;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,UAAU;IACtCC,IAAI,eAAE/B,OAAA,CAACN,cAAc;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,UAAU;IACtCC,IAAI,eAAE/B,OAAA,CAACL,gBAAgB;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,WAAW;IACvCC,IAAI,eAAE/B,OAAA,CAACJ,eAAe;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,QAAQ;IACpCC,IAAI,eAAE/B,OAAA,CAACZ,aAAa;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,IAAI;IACXhC,QAAQ,EAAE,CACR;MACEyB,GAAG,EAAE,yBAAyBD,OAAO,CAACE,EAAE,EAAE;MAC1CC,IAAI,eAAE/B,OAAA,CAACZ,aAAa;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,KAAK,EAAE;IACT,CAAC,EACD;MACEP,GAAG,EAAE,oBAAoBD,OAAO,CAACE,EAAE,EAAE;MACrCC,IAAI,eAAE/B,OAAA,CAACZ,aAAa;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,KAAK,EAAE;IACT,CAAC,EACD;MACEP,GAAG,EAAE,qBAAqBD,OAAO,CAACE,EAAE,EAAE;MACtCC,IAAI,eAAE/B,OAAA,CAACX,eAAe;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzBC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,CACF;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IACER,GAAG,EAAE,GAAG;IACRE,IAAI,eAAE/B,OAAA,CAAChB,iBAAiB;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,oBAAoB;IACzBE,IAAI,eAAE/B,OAAA,CAACf,eAAe;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACbhC,QAAQ,EAAE,CACR;MACEyB,GAAG,EAAE,WAAW;MAChBE,IAAI,eAAE/B,OAAA,CAACf,eAAe;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzBC,KAAK,EAAE;IACT,CAAC,EACD,GAAG5B,QAAQ,CAAC8B,GAAG,CAACV,OAAO,KAAK;MAC1BC,GAAG,EAAE,WAAWD,OAAO,CAACE,EAAE,EAAE;MAC5BC,IAAI,eAAE/B,OAAA,CAACb,YAAY;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,KAAK,EAAER,OAAO,CAACW,IAAI;MACnBnC,QAAQ,EAAEuB,mBAAmB,CAACC,OAAO;IACvC,CAAC,CAAC,CAAC;EAEP,CAAC,CACF;;EAID;EACA,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMxB,QAAQ,GAAGH,QAAQ,CAACG,QAAQ;;IAElC;IACA,IAAIA,QAAQ,CAACyB,UAAU,CAAC,YAAY,CAAC,EAAE;MACrC,OAAO,CAACzB,QAAQ,CAAC;IACnB;;IAEA;IACA,OAAO,CAACA,QAAQ,CAAC;EACnB,CAAC;;EAED;EACA,MAAM0B,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAM1B,QAAQ,GAAGH,QAAQ,CAACG,QAAQ;IAClC,MAAM2B,QAAQ,GAAG,CAAC,oBAAoB,CAAC;;IAEvC;IACA,IAAI3B,QAAQ,CAACyB,UAAU,CAAC,YAAY,CAAC,IAAIvB,SAAS,EAAE;MAClDyB,QAAQ,CAACC,IAAI,CAAC,WAAW1B,SAAS,EAAE,CAAC;IACvC;IAEA,OAAOyB,QAAQ;EACjB,CAAC;;EAED;EACA,MAAME,aAAa,GAAG,CACpB;IACEhB,GAAG,EAAE,SAAS;IACdE,IAAI,eAAE/B,OAAA,CAACd,YAAY;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,UAAU;IACfE,IAAI,eAAE/B,OAAA,CAACX,eAAe;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEU,IAAI,EAAE;EACR,CAAC,EACD;IACEjB,GAAG,EAAE,QAAQ;IACbE,IAAI,eAAE/B,OAAA,CAACR,cAAc;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMW,eAAe,GAAGA,CAAC;IAAElB;EAAI,CAAC,KAAK;IACnC;IACA,IAAIA,GAAG,KAAK,oBAAoB,IAAIA,GAAG,CAACY,UAAU,CAAC,UAAU,CAAC,EAAE;MAC9D;IACF;IACA7B,QAAQ,CAACiB,GAAG,CAAC;EACf,CAAC;EAED,MAAMmB,mBAAmB,GAAGA,CAAC;IAAEnB;EAAI,CAAC,KAAK;IACvC,QAAQA,GAAG;MACT,KAAK,SAAS;QACZjB,QAAQ,CAAC,UAAU,CAAC;QACpB;MACF,KAAK,UAAU;QACbA,QAAQ,CAAC,WAAW,CAAC;QACrB;MACF,KAAK,QAAQ;QACX;QACAc,OAAO,CAACuB,GAAG,CAAC,MAAM,CAAC;QACnB;MACF;QACE;IACJ;EACF,CAAC;EAED,oBACEjD,OAAA,CAACxB,SAAS;IAAC0E,SAAS,EAAC,kBAAkB;IAAA9C,QAAA,gBACrCJ,OAAA,CAACC,MAAM;MAACiD,SAAS,EAAC,eAAe;MAAA9C,QAAA,eAC/BJ,OAAA;QAAKmD,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAlD,QAAA,gBACrFJ,OAAA;UAAKmD,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAjD,QAAA,gBACpDJ,OAAA,CAACpB,MAAM;YACLkE,IAAI,EAAC,MAAM;YACXf,IAAI,EAAEzB,SAAS,gBAAGN,OAAA,CAACT,kBAAkB;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGnC,OAAA,CAACV,gBAAgB;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChEoB,OAAO,EAAEA,CAAA,KAAMhD,YAAY,CAAC,CAACD,SAAS,CAAE;YACxC6C,KAAK,EAAE;cAAEK,WAAW,EAAE;YAAG;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACFnC,OAAA;YAAImD,KAAK,EAAE;cAAEM,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAAxD,QAAA,EAAC;UAElF;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENnC,OAAA,CAACnB,KAAK;UAAAuB,QAAA,gBACJJ,OAAA,CAACpB,MAAM;YAACkE,IAAI,EAAC,MAAM;YAACf,IAAI,eAAE/B,OAAA,CAACP,YAAY;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9CnC,OAAA,CAACrB,QAAQ;YACPkF,IAAI,EAAE;cACJC,KAAK,EAAEjB,aAAa;cACpBU,OAAO,EAAEP;YACX,CAAE;YACFe,SAAS,EAAC,aAAa;YAAA3D,QAAA,eAEvBJ,OAAA,CAACnB,KAAK;cAACsE,KAAK,EAAE;gBAAEa,MAAM,EAAE;cAAU,CAAE;cAAA5D,QAAA,gBAClCJ,OAAA,CAACtB,MAAM;gBAACqD,IAAI,eAAE/B,OAAA,CAACd,YAAY;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCnC,OAAA;gBAAAI,QAAA,EAAM;cAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETnC,OAAA,CAACxB,SAAS;MAAC0E,SAAS,EAAC,gBAAgB;MAAA9C,QAAA,gBACnCJ,OAAA,CAACE,KAAK;QACJgD,SAAS,EAAC,cAAc;QACxB5C,SAAS,EAAEA,SAAU;QACrB2D,KAAK,EAAE,GAAI;QACXC,cAAc,EAAE,EAAG;QACnBC,KAAK,EAAC,OAAO;QAAA/D,QAAA,eAEbJ,OAAA;UAAKmD,KAAK,EAAE;YAAEiB,MAAM,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAjE,QAAA,eAEhDJ,OAAA,CAACvB,IAAI;YACH6F,IAAI,EAAC,QAAQ;YACbC,YAAY,EAAE/B,eAAe,CAAC,CAAE;YAChCsB,KAAK,EAAEzB,aAAc;YACrBkB,OAAO,EAAER,eAAgB;YACzBI,KAAK,EAAE;cAAEqB,WAAW,EAAE;YAAE,CAAE;YAC1BC,eAAe,EAAE/B,kBAAkB,CAAC,CAAE;YACtCC,QAAQ,EAAED,kBAAkB,CAAC;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERnC,OAAA,CAACG,OAAO;QAAC+C,SAAS,EAAC,aAAa;QAAA9C,QAAA,EAC7BA;MAAQ;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAAC9B,EAAA,CA9PI9B,MAAM;EAAA,QAIOO,WAAW,EACXC,WAAW;AAAA;AAAA2F,EAAA,GALxBnG,MAAM;AAgQZ,eAAeA,MAAM;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}