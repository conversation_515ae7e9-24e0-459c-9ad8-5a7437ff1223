{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { DashboardOutlined, ProjectOutlined, UserOutlined, BookOutlined, FileTextOutlined, RobotOutlined, SettingOutlined, MenuFoldOutlined, MenuUnfoldOutlined, LogoutOutlined, BellOutlined, FolderOutlined, DatabaseOutlined, ControlOutlined, EyeOutlined } from '@ant-design/icons';\nimport { projectAPI } from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取当前项目ID（如果在项目页面中）\n  const getProjectId = () => {\n    const pathParts = location.pathname.split('/');\n    if (pathParts[1] === 'projects' && pathParts[2]) {\n      return pathParts[2];\n    }\n    return null;\n  };\n  const projectId = getProjectId();\n\n  // 加载项目列表\n  useEffect(() => {\n    loadProjects();\n  }, []);\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      const response = await projectAPI.getProjects();\n      const projectsData = response.data.projects || [];\n      setProjects(projectsData);\n    } catch (error) {\n      console.error('加载项目列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 构建项目子菜单\n  const buildProjectSubMenu = project => [{\n    key: `/projects/${project.id}`,\n    icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this),\n    label: '项目概览'\n  }, {\n    key: `/projects/${project.id}/volumes`,\n    icon: /*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 13\n    }, this),\n    label: '卷宗管理'\n  }, {\n    key: `/projects/${project.id}/content`,\n    icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this),\n    label: '内容管理'\n  }, {\n    key: `/projects/${project.id}/settings`,\n    icon: /*#__PURE__*/_jsxDEV(ControlOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this),\n    label: '设定管理'\n  }, {\n    key: `/projects/${project.id}/tools`,\n    icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 13\n    }, this),\n    label: '工具',\n    children: [{\n      key: `/ai-assistant?project=${project.id}`,\n      icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 17\n      }, this),\n      label: 'Agent-AI助手'\n    }, {\n      key: `/ai-test?project=${project.id}`,\n      icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 17\n      }, this),\n      label: 'Agent-AI测试'\n    }, {\n      key: `/settings?project=${project.id}`,\n      icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 17\n      }, this),\n      label: '系统设置'\n    }]\n  }];\n\n  // 主菜单项\n  const mainMenuItems = [{\n    key: '/',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 13\n    }, this),\n    label: '仪表盘'\n  }, {\n    key: 'project-management',\n    icon: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 13\n    }, this),\n    label: '项目管理',\n    children: [{\n      key: '/projects',\n      icon: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 17\n      }, this),\n      label: '项目列表'\n    }, ...projects.map(project => ({\n      key: `project-${project.id}`,\n      icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 17\n      }, this),\n      label: project.name,\n      children: buildProjectSubMenu(project)\n    }))]\n  }];\n\n  // 用户菜单\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 13\n    }, this),\n    label: '个人资料'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 13\n    }, this),\n    label: '偏好设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 13\n    }, this),\n    label: '退出登录'\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    // 如果是项目管理或项目分组的key，不进行导航\n    if (key === 'project-management' || key.startsWith('project-')) {\n      return;\n    }\n    navigate(key);\n  };\n  const handleUserMenuClick = ({\n    key\n  }) => {\n    switch (key) {\n      case 'profile':\n        navigate('/profile');\n        break;\n      case 'settings':\n        navigate('/settings');\n        break;\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      default:\n        break;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    className: \"layout-container\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      className: \"layout-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 58\n            }, this),\n            onClick: () => setCollapsed(!collapsed),\n            style: {\n              marginRight: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              color: '#1890ff',\n              fontSize: '20px',\n              fontWeight: 'bold'\n            },\n            children: \"NovelCraft\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 39\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems,\n              onClick: handleUserMenuClick\n            },\n            placement: \"bottomRight\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u7528\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      className: \"layout-content\",\n      children: [/*#__PURE__*/_jsxDEV(Sider, {\n        className: \"layout-sider\",\n        collapsed: collapsed,\n        width: 240,\n        collapsedWidth: 80,\n        theme: \"light\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '100%',\n            overflowY: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(Menu, {\n            mode: \"inline\",\n            selectedKeys: [location.pathname],\n            items: mainMenuItems,\n            onClick: handleMenuClick,\n            style: {\n              borderRight: 0\n            },\n            defaultOpenKeys: ['project-management']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        className: \"layout-main\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"U2uVLKJEKesVhGwcvCnkYn43IhU=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "AntLayout", "<PERSON><PERSON>", "Avatar", "Dropdown", "<PERSON><PERSON>", "Space", "useNavigate", "useLocation", "DashboardOutlined", "ProjectOutlined", "UserOutlined", "BookOutlined", "FileTextOutlined", "RobotOutlined", "SettingOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "LogoutOutlined", "BellOutlined", "FolderOutlined", "DatabaseOutlined", "ControlOutlined", "EyeOutlined", "projectAPI", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "children", "_s", "collapsed", "setCollapsed", "projects", "setProjects", "loading", "setLoading", "navigate", "location", "getProjectId", "pathParts", "pathname", "split", "projectId", "loadProjects", "response", "getProjects", "projectsData", "data", "error", "console", "buildProjectSubMenu", "project", "key", "id", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "mainMenuItems", "map", "name", "userMenuItems", "type", "handleMenuClick", "startsWith", "handleUserMenuClick", "log", "className", "style", "display", "alignItems", "justifyContent", "onClick", "marginRight", "margin", "color", "fontSize", "fontWeight", "menu", "items", "placement", "cursor", "width", "collapsedWidth", "theme", "height", "overflowY", "mode", "<PERSON><PERSON><PERSON><PERSON>", "borderRight", "defaultOpenKeys", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/components/Layout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  DashboardOutlined,\n  ProjectOutlined,\n  UserOutlined,\n  BookOutlined,\n  FileTextOutlined,\n  RobotOutlined,\n  SettingOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  LogoutOutlined,\n  BellOutlined,\n  FolderOutlined,\n  DatabaseOutlined,\n  ControlOutlined,\n  EyeOutlined\n} from '@ant-design/icons';\nimport { projectAPI } from '../utils/api';\n\nconst { Header, Sider, Content } = AntLayout;\n\nconst Layout = ({ children }) => {\n  const [collapsed, setCollapsed] = useState(false);\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取当前项目ID（如果在项目页面中）\n  const getProjectId = () => {\n    const pathParts = location.pathname.split('/');\n    if (pathParts[1] === 'projects' && pathParts[2]) {\n      return pathParts[2];\n    }\n    return null;\n  };\n\n  const projectId = getProjectId();\n\n  // 加载项目列表\n  useEffect(() => {\n    loadProjects();\n  }, []);\n\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      const response = await projectAPI.getProjects();\n      const projectsData = response.data.projects || [];\n      setProjects(projectsData);\n    } catch (error) {\n      console.error('加载项目列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 构建项目子菜单\n  const buildProjectSubMenu = (project) => [\n    {\n      key: `/projects/${project.id}`,\n      icon: <EyeOutlined />,\n      label: '项目概览',\n    },\n    {\n      key: `/projects/${project.id}/volumes`,\n      icon: <FolderOutlined />,\n      label: '卷宗管理',\n    },\n    {\n      key: `/projects/${project.id}/content`,\n      icon: <DatabaseOutlined />,\n      label: '内容管理',\n    },\n    {\n      key: `/projects/${project.id}/settings`,\n      icon: <ControlOutlined />,\n      label: '设定管理',\n    },\n    {\n      key: `/projects/${project.id}/tools`,\n      icon: <RobotOutlined />,\n      label: '工具',\n      children: [\n        {\n          key: `/ai-assistant?project=${project.id}`,\n          icon: <RobotOutlined />,\n          label: 'Agent-AI助手',\n        },\n        {\n          key: `/ai-test?project=${project.id}`,\n          icon: <RobotOutlined />,\n          label: 'Agent-AI测试',\n        },\n        {\n          key: `/settings?project=${project.id}`,\n          icon: <SettingOutlined />,\n          label: '系统设置',\n        },\n      ],\n    },\n  ];\n\n  // 主菜单项\n  const mainMenuItems = [\n    {\n      key: '/',\n      icon: <DashboardOutlined />,\n      label: '仪表盘',\n    },\n    {\n      key: 'project-management',\n      icon: <ProjectOutlined />,\n      label: '项目管理',\n      children: [\n        {\n          key: '/projects',\n          icon: <ProjectOutlined />,\n          label: '项目列表',\n        },\n        ...projects.map(project => ({\n          key: `project-${project.id}`,\n          icon: <BookOutlined />,\n          label: project.name,\n          children: buildProjectSubMenu(project),\n        })),\n      ],\n    },\n  ];\n\n  // 用户菜单\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '偏好设置',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n    },\n  ];\n\n  const handleMenuClick = ({ key }) => {\n    // 如果是项目管理或项目分组的key，不进行导航\n    if (key === 'project-management' || key.startsWith('project-')) {\n      return;\n    }\n    navigate(key);\n  };\n\n  const handleUserMenuClick = ({ key }) => {\n    switch (key) {\n      case 'profile':\n        navigate('/profile');\n        break;\n      case 'settings':\n        navigate('/settings');\n        break;\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      default:\n        break;\n    }\n  };\n\n  return (\n    <AntLayout className=\"layout-container\">\n      <Header className=\"layout-header\">\n        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <div style={{ display: 'flex', alignItems: 'center' }}>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={() => setCollapsed(!collapsed)}\n              style={{ marginRight: 16 }}\n            />\n            <h1 style={{ margin: 0, color: '#1890ff', fontSize: '20px', fontWeight: 'bold' }}>\n              NovelCraft\n            </h1>\n          </div>\n\n          <Space>\n            <Button type=\"text\" icon={<BellOutlined />} />\n            <Dropdown\n              menu={{\n                items: userMenuItems,\n                onClick: handleUserMenuClick,\n              }}\n              placement=\"bottomRight\"\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <span>用户</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </div>\n      </Header>\n\n      <AntLayout className=\"layout-content\">\n        <Sider\n          className=\"layout-sider\"\n          collapsed={collapsed}\n          width={240}\n          collapsedWidth={80}\n          theme=\"light\"\n        >\n          <div style={{ height: '100%', overflowY: 'auto' }}>\n            {/* 主菜单 */}\n            <Menu\n              mode=\"inline\"\n              selectedKeys={[location.pathname]}\n              items={mainMenuItems}\n              onClick={handleMenuClick}\n              style={{ borderRight: 0 }}\n              defaultOpenKeys={['project-management']}\n            />\n          </div>\n        </Sider>\n\n        <Content className=\"layout-main\">\n          {children}\n        </Content>\n      </AntLayout>\n    </AntLayout>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,IAAIC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AACjF,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,gBAAgB,EAChBC,eAAe,EACfC,WAAW,QACN,mBAAmB;AAC1B,SAASC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAG5B,SAAS;AAE5C,MAAMD,MAAM,GAAGA,CAAC;EAAE8B;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMwC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMgC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAGF,QAAQ,CAACG,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC;IAC9C,IAAIF,SAAS,CAAC,CAAC,CAAC,KAAK,UAAU,IAAIA,SAAS,CAAC,CAAC,CAAC,EAAE;MAC/C,OAAOA,SAAS,CAAC,CAAC,CAAC;IACrB;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMG,SAAS,GAAGJ,YAAY,CAAC,CAAC;;EAEhC;EACAzC,SAAS,CAAC,MAAM;IACd8C,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BR,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMtB,UAAU,CAACuB,WAAW,CAAC,CAAC;MAC/C,MAAMC,YAAY,GAAGF,QAAQ,CAACG,IAAI,CAACf,QAAQ,IAAI,EAAE;MACjDC,WAAW,CAACa,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMe,mBAAmB,GAAIC,OAAO,IAAK,CACvC;IACEC,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,EAAE;IAC9BC,IAAI,eAAE9B,OAAA,CAACH,WAAW;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,UAAU;IACtCC,IAAI,eAAE9B,OAAA,CAACN,cAAc;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,UAAU;IACtCC,IAAI,eAAE9B,OAAA,CAACL,gBAAgB;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,WAAW;IACvCC,IAAI,eAAE9B,OAAA,CAACJ,eAAe;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,QAAQ;IACpCC,IAAI,eAAE9B,OAAA,CAACZ,aAAa;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,IAAI;IACX/B,QAAQ,EAAE,CACR;MACEwB,GAAG,EAAE,yBAAyBD,OAAO,CAACE,EAAE,EAAE;MAC1CC,IAAI,eAAE9B,OAAA,CAACZ,aAAa;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,KAAK,EAAE;IACT,CAAC,EACD;MACEP,GAAG,EAAE,oBAAoBD,OAAO,CAACE,EAAE,EAAE;MACrCC,IAAI,eAAE9B,OAAA,CAACZ,aAAa;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,KAAK,EAAE;IACT,CAAC,EACD;MACEP,GAAG,EAAE,qBAAqBD,OAAO,CAACE,EAAE,EAAE;MACtCC,IAAI,eAAE9B,OAAA,CAACX,eAAe;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzBC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,CACF;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IACER,GAAG,EAAE,GAAG;IACRE,IAAI,eAAE9B,OAAA,CAACjB,iBAAiB;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,oBAAoB;IACzBE,IAAI,eAAE9B,OAAA,CAAChB,eAAe;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACb/B,QAAQ,EAAE,CACR;MACEwB,GAAG,EAAE,WAAW;MAChBE,IAAI,eAAE9B,OAAA,CAAChB,eAAe;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzBC,KAAK,EAAE;IACT,CAAC,EACD,GAAG3B,QAAQ,CAAC6B,GAAG,CAACV,OAAO,KAAK;MAC1BC,GAAG,EAAE,WAAWD,OAAO,CAACE,EAAE,EAAE;MAC5BC,IAAI,eAAE9B,OAAA,CAACd,YAAY;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,KAAK,EAAER,OAAO,CAACW,IAAI;MACnBlC,QAAQ,EAAEsB,mBAAmB,CAACC,OAAO;IACvC,CAAC,CAAC,CAAC;EAEP,CAAC,CACF;;EAED;EACA,MAAMY,aAAa,GAAG,CACpB;IACEX,GAAG,EAAE,SAAS;IACdE,IAAI,eAAE9B,OAAA,CAACf,YAAY;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,UAAU;IACfE,IAAI,eAAE9B,OAAA,CAACX,eAAe;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEK,IAAI,EAAE;EACR,CAAC,EACD;IACEZ,GAAG,EAAE,QAAQ;IACbE,IAAI,eAAE9B,OAAA,CAACR,cAAc;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMM,eAAe,GAAGA,CAAC;IAAEb;EAAI,CAAC,KAAK;IACnC;IACA,IAAIA,GAAG,KAAK,oBAAoB,IAAIA,GAAG,CAACc,UAAU,CAAC,UAAU,CAAC,EAAE;MAC9D;IACF;IACA9B,QAAQ,CAACgB,GAAG,CAAC;EACf,CAAC;EAED,MAAMe,mBAAmB,GAAGA,CAAC;IAAEf;EAAI,CAAC,KAAK;IACvC,QAAQA,GAAG;MACT,KAAK,SAAS;QACZhB,QAAQ,CAAC,UAAU,CAAC;QACpB;MACF,KAAK,UAAU;QACbA,QAAQ,CAAC,WAAW,CAAC;QACrB;MACF,KAAK,QAAQ;QACX;QACAa,OAAO,CAACmB,GAAG,CAAC,MAAM,CAAC;QACnB;MACF;QACE;IACJ;EACF,CAAC;EAED,oBACE5C,OAAA,CAACzB,SAAS;IAACsE,SAAS,EAAC,kBAAkB;IAAAzC,QAAA,gBACrCJ,OAAA,CAACC,MAAM;MAAC4C,SAAS,EAAC,eAAe;MAAAzC,QAAA,eAC/BJ,OAAA;QAAK8C,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAA7C,QAAA,gBACrFJ,OAAA;UAAK8C,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAA5C,QAAA,gBACpDJ,OAAA,CAACrB,MAAM;YACL6D,IAAI,EAAC,MAAM;YACXV,IAAI,EAAExB,SAAS,gBAAGN,OAAA,CAACT,kBAAkB;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlC,OAAA,CAACV,gBAAgB;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChEgB,OAAO,EAAEA,CAAA,KAAM3C,YAAY,CAAC,CAACD,SAAS,CAAE;YACxCwC,KAAK,EAAE;cAAEK,WAAW,EAAE;YAAG;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACFlC,OAAA;YAAI8C,KAAK,EAAE;cAAEM,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAAnD,QAAA,EAAC;UAElF;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENlC,OAAA,CAACpB,KAAK;UAAAwB,QAAA,gBACJJ,OAAA,CAACrB,MAAM;YAAC6D,IAAI,EAAC,MAAM;YAACV,IAAI,eAAE9B,OAAA,CAACP,YAAY;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9ClC,OAAA,CAACtB,QAAQ;YACP8E,IAAI,EAAE;cACJC,KAAK,EAAElB,aAAa;cACpBW,OAAO,EAAEP;YACX,CAAE;YACFe,SAAS,EAAC,aAAa;YAAAtD,QAAA,eAEvBJ,OAAA,CAACpB,KAAK;cAACkE,KAAK,EAAE;gBAAEa,MAAM,EAAE;cAAU,CAAE;cAAAvD,QAAA,gBAClCJ,OAAA,CAACvB,MAAM;gBAACqD,IAAI,eAAE9B,OAAA,CAACf,YAAY;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClClC,OAAA;gBAAAI,QAAA,EAAM;cAAE;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETlC,OAAA,CAACzB,SAAS;MAACsE,SAAS,EAAC,gBAAgB;MAAAzC,QAAA,gBACnCJ,OAAA,CAACE,KAAK;QACJ2C,SAAS,EAAC,cAAc;QACxBvC,SAAS,EAAEA,SAAU;QACrBsD,KAAK,EAAE,GAAI;QACXC,cAAc,EAAE,EAAG;QACnBC,KAAK,EAAC,OAAO;QAAA1D,QAAA,eAEbJ,OAAA;UAAK8C,KAAK,EAAE;YAAEiB,MAAM,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAA5D,QAAA,eAEhDJ,OAAA,CAACxB,IAAI;YACHyF,IAAI,EAAC,QAAQ;YACbC,YAAY,EAAE,CAACrD,QAAQ,CAACG,QAAQ,CAAE;YAClCyC,KAAK,EAAErB,aAAc;YACrBc,OAAO,EAAET,eAAgB;YACzBK,KAAK,EAAE;cAAEqB,WAAW,EAAE;YAAE,CAAE;YAC1BC,eAAe,EAAE,CAAC,oBAAoB;UAAE;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERlC,OAAA,CAACG,OAAO;QAAC0C,SAAS,EAAC,aAAa;QAAAzC,QAAA,EAC7BA;MAAQ;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAAC7B,EAAA,CAzNI/B,MAAM;EAAA,QAIOO,WAAW,EACXC,WAAW;AAAA;AAAAuF,EAAA,GALxB/F,MAAM;AA2NZ,eAAeA,MAAM;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}