{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\ProjectList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Tag, Progress, Modal, Form, Input, Select, message, Popconfirm, Typography, Row, Col } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, CopyOutlined, ExportOutlined, ImportOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { projectAPI } from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\nconst ProjectList = () => {\n  _s();\n  const navigate = useNavigate();\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState(null);\n  const [form] = Form.useForm();\n  useEffect(() => {\n    loadProjects();\n  }, []);\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      const response = await projectAPI.getProjects();\n      const projectsData = response.data.projects || [];\n\n      // 格式化项目数据\n      const formattedProjects = projectsData.map(project => {\n        var _project$progress;\n        return {\n          id: project.id,\n          name: project.name,\n          title: project.title,\n          author: project.author,\n          type: project.project_type,\n          status: project.status,\n          summary: project.summary,\n          wordCount: project.word_count || 0,\n          chapterCount: project.chapter_count || 0,\n          characterCount: project.character_count || 0,\n          progress: ((_project$progress = project.progress) === null || _project$progress === void 0 ? void 0 : _project$progress.completion_percentage) || 0,\n          createdAt: new Date(project.created_at).toLocaleDateString(),\n          updatedAt: new Date(project.updated_at).toLocaleDateString(),\n          isPreset: project.is_preset || false\n        };\n      });\n      setProjects(formattedProjects);\n    } catch (error) {\n      console.error('加载项目列表失败:', error);\n      message.error('加载项目列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getStatusColor = status => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n  const getStatusText = status => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n  const getTypeText = type => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n  const handleCreate = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = project => {\n    setEditingProject(project);\n    form.setFieldsValue(project);\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      // 调用后端API删除项目\n      await projectAPI.deleteProject(id);\n\n      // 删除成功后重新加载项目列表\n      message.success('项目删除成功');\n      loadProjects();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('删除项目失败:', error);\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '删除项目失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      if (editingProject) {\n        // 更新项目\n        await projectAPI.updateProject(editingProject.id, {\n          ...values,\n          project_type: values.type\n        });\n        message.success('项目更新成功');\n      } else {\n        // 创建项目\n        await projectAPI.createProject({\n          ...values,\n          project_type: values.type\n        });\n        message.success('项目创建成功');\n      }\n      setModalVisible(false);\n      loadProjects(); // 重新加载项目列表\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('操作失败:', error);\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '操作失败');\n    }\n  };\n  const handleDuplicate = async project => {\n    try {\n      await projectAPI.duplicateProject(project.id, `${project.name} (副本)`);\n      message.success('项目复制成功');\n      loadProjects(); // 重新加载项目列表\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('复制项目失败:', error);\n      message.error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || '复制项目失败');\n    }\n  };\n  const columns = [{\n    title: '项目名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: [text, record.isPreset && /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"blue\",\n          size: \"small\",\n          children: \"\\u9884\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this), record.title && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '作者',\n    dataIndex: 'author',\n    key: 'author'\n  }, {\n    title: '类型',\n    dataIndex: 'type',\n    key: 'type',\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      children: getTypeText(type)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 25\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '进度',\n    dataIndex: 'progress',\n    key: 'progress',\n    render: progress => /*#__PURE__*/_jsxDEV(Progress, {\n      percent: progress,\n      size: \"small\",\n      status: progress === 100 ? 'success' : 'active'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '统计',\n    key: 'stats',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u5B57\\u6570: \", (record.wordCount / 10000).toFixed(1), \"\\u4E07\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u7AE0\\u8282: \", record.chapterCount]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u4EBA\\u7269: \", record.characterCount]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '最后修改',\n    dataIndex: 'updatedAt',\n    key: 'updatedAt'\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 19\n        }, this),\n        onClick: () => navigate(`/projects/${record.id}`),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDuplicate(record),\n        children: \"\\u590D\\u5236\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u9879\\u76EE\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 46\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u9879\\u76EE\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-left\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 21\n            }, this),\n            onClick: handleCreate,\n            children: \"\\u65B0\\u5EFA\\u9879\\u76EE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-right\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ImportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 29\n              }, this),\n              children: \"\\u5BFC\\u5165\\u9879\\u76EE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 29\n              }, this),\n              children: \"\\u5BFC\\u51FA\\u9879\\u76EE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: projects,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个项目`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingProject ? '编辑项目' : '新建项目',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u9879\\u76EE\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入项目名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u9879\\u76EE\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u5C0F\\u8BF4\\u6807\\u9898\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5C0F\\u8BF4\\u6807\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"author\",\n              label: \"\\u4F5C\\u8005\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4F5C\\u8005\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"type\",\n              label: \"\\u9879\\u76EE\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择项目类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u9879\\u76EE\\u7C7B\\u578B\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"fantasy\",\n                  children: \"\\u5947\\u5E7B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"xianxia\",\n                  children: \"\\u4ED9\\u4FA0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"wuxia\",\n                  children: \"\\u6B66\\u4FA0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"scifi\",\n                  children: \"\\u79D1\\u5E7B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"modern\",\n                  children: \"\\u73B0\\u4EE3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"historical\",\n                  children: \"\\u5386\\u53F2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"romance\",\n                  children: \"\\u8A00\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"summary\",\n          label: \"\\u9879\\u76EE\\u7B80\\u4ECB\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u9879\\u76EE\\u7B80\\u4ECB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 287,\n    columnNumber: 5\n  }, this);\n};\n_s(ProjectList, \"g2W8o9nUU2hYGxU5aQ47s0oeDt4=\", false, function () {\n  return [useNavigate, Form.useForm];\n});\n_c = ProjectList;\nexport default ProjectList;\nvar _c;\n$RefreshReg$(_c, \"ProjectList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Tag", "Progress", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "Typography", "Row", "Col", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "CopyOutlined", "ExportOutlined", "ImportOutlined", "useNavigate", "projectAPI", "jsxDEV", "_jsxDEV", "Title", "Option", "TextArea", "ProjectList", "_s", "navigate", "projects", "setProjects", "loading", "setLoading", "modalVisible", "setModalVisible", "editingProject", "setEditingProject", "form", "useForm", "loadProjects", "response", "getProjects", "projectsData", "data", "formattedProjects", "map", "project", "_project$progress", "id", "name", "title", "author", "type", "project_type", "status", "summary", "wordCount", "word_count", "chapterCount", "chapter_count", "characterCount", "character_count", "progress", "completion_percentage", "createdAt", "Date", "created_at", "toLocaleDateString", "updatedAt", "updated_at", "isPreset", "is_preset", "error", "console", "getStatusColor", "colors", "planning", "writing", "reviewing", "completed", "published", "getStatusText", "texts", "getTypeText", "fantasy", "xianxia", "wuxia", "scifi", "modern", "historical", "romance", "handleCreate", "resetFields", "handleEdit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "deleteProject", "success", "_error$response", "_error$response$data", "detail", "handleSubmit", "values", "updateProject", "createProject", "_error$response2", "_error$response2$data", "handleDuplicate", "duplicateProject", "_error$response3", "_error$response3$data", "columns", "dataIndex", "key", "render", "text", "record", "children", "style", "fontWeight", "display", "alignItems", "gap", "color", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "percent", "_", "toFixed", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "className", "level", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onCancel", "onOk", "submit", "width", "layout", "onFinish", "gutter", "span", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "value", "rows", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/ProjectList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Tag,\n  Progress,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Typography,\n  Row,\n  Col\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  CopyOutlined,\n  ExportOutlined,\n  ImportOutlined\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { projectAPI } from '../utils/api';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst ProjectList = () => {\n  const navigate = useNavigate();\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState(null);\n  const [form] = Form.useForm();\n\n\n\n  useEffect(() => {\n    loadProjects();\n  }, []);\n\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      const response = await projectAPI.getProjects();\n      const projectsData = response.data.projects || [];\n\n      // 格式化项目数据\n      const formattedProjects = projectsData.map(project => ({\n        id: project.id,\n        name: project.name,\n        title: project.title,\n        author: project.author,\n        type: project.project_type,\n        status: project.status,\n        summary: project.summary,\n        wordCount: project.word_count || 0,\n        chapterCount: project.chapter_count || 0,\n        characterCount: project.character_count || 0,\n        progress: project.progress?.completion_percentage || 0,\n        createdAt: new Date(project.created_at).toLocaleDateString(),\n        updatedAt: new Date(project.updated_at).toLocaleDateString(),\n        isPreset: project.is_preset || false\n      }));\n\n      setProjects(formattedProjects);\n    } catch (error) {\n      console.error('加载项目列表失败:', error);\n      message.error('加载项目列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getStatusText = (status) => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n\n  const getTypeText = (type) => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n\n  const handleCreate = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (project) => {\n    setEditingProject(project);\n    form.setFieldsValue(project);\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      // 调用后端API删除项目\n      await projectAPI.deleteProject(id);\n\n      // 删除成功后重新加载项目列表\n      message.success('项目删除成功');\n      loadProjects();\n    } catch (error) {\n      console.error('删除项目失败:', error);\n      message.error(error.response?.data?.detail || '删除项目失败');\n    }\n  };\n\n  const handleSubmit = async (values) => {\n    try {\n      if (editingProject) {\n        // 更新项目\n        await projectAPI.updateProject(editingProject.id, {\n          ...values,\n          project_type: values.type\n        });\n        message.success('项目更新成功');\n      } else {\n        // 创建项目\n        await projectAPI.createProject({\n          ...values,\n          project_type: values.type\n        });\n        message.success('项目创建成功');\n      }\n      setModalVisible(false);\n      loadProjects(); // 重新加载项目列表\n    } catch (error) {\n      console.error('操作失败:', error);\n      message.error(error.response?.data?.detail || '操作失败');\n    }\n  };\n\n  const handleDuplicate = async (project) => {\n    try {\n      await projectAPI.duplicateProject(project.id, `${project.name} (副本)`);\n      message.success('项目复制成功');\n      loadProjects(); // 重新加载项目列表\n    } catch (error) {\n      console.error('复制项目失败:', error);\n      message.error(error.response?.data?.detail || '复制项目失败');\n    }\n  };\n\n  const columns = [\n    {\n      title: '项目名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <div>\n          <div style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: '8px' }}>\n            {text}\n            {record.isPreset && (\n              <Tag color=\"blue\" size=\"small\">预置</Tag>\n            )}\n          </div>\n          {record.title && (\n            <div style={{ fontSize: '12px', color: '#666' }}>{record.title}</div>\n          )}\n        </div>\n      ),\n    },\n    {\n      title: '作者',\n      dataIndex: 'author',\n      key: 'author',\n    },\n    {\n      title: '类型',\n      dataIndex: 'type',\n      key: 'type',\n      render: (type) => <Tag>{getTypeText(type)}</Tag>,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>\n      ),\n    },\n    {\n      title: '进度',\n      dataIndex: 'progress',\n      key: 'progress',\n      render: (progress) => (\n        <Progress\n          percent={progress}\n          size=\"small\"\n          status={progress === 100 ? 'success' : 'active'}\n        />\n      ),\n    },\n    {\n      title: '统计',\n      key: 'stats',\n      render: (_, record) => (\n        <div style={{ fontSize: '12px' }}>\n          <div>字数: {(record.wordCount / 10000).toFixed(1)}万</div>\n          <div>章节: {record.chapterCount}</div>\n          <div>人物: {record.characterCount}</div>\n        </div>\n      ),\n    },\n    {\n      title: '最后修改',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            icon={<EyeOutlined />}\n            onClick={() => navigate(`/projects/${record.id}`)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Button\n            type=\"link\"\n            icon={<CopyOutlined />}\n            onClick={() => handleDuplicate(record)}\n          >\n            复制\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个项目吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button type=\"link\" danger icon={<DeleteOutlined />}>\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">项目管理</Title>\n      </div>\n\n      <Card>\n        <div className=\"toolbar\">\n          <div className=\"toolbar-left\">\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleCreate}\n            >\n              新建项目\n            </Button>\n          </div>\n          <div className=\"toolbar-right\">\n            <Space>\n              <Button icon={<ImportOutlined />}>导入项目</Button>\n              <Button icon={<ExportOutlined />}>导出项目</Button>\n            </Space>\n          </div>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={projects}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个项目`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingProject ? '编辑项目' : '新建项目'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"项目名称\"\n                rules={[{ required: true, message: '请输入项目名称' }]}\n              >\n                <Input placeholder=\"请输入项目名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"title\"\n                label=\"小说标题\"\n              >\n                <Input placeholder=\"请输入小说标题\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"author\"\n                label=\"作者\"\n              >\n                <Input placeholder=\"请输入作者名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"type\"\n                label=\"项目类型\"\n                rules={[{ required: true, message: '请选择项目类型' }]}\n              >\n                <Select placeholder=\"请选择项目类型\">\n                  <Option value=\"fantasy\">奇幻</Option>\n                  <Option value=\"xianxia\">仙侠</Option>\n                  <Option value=\"wuxia\">武侠</Option>\n                  <Option value=\"scifi\">科幻</Option>\n                  <Option value=\"modern\">现代</Option>\n                  <Option value=\"historical\">历史</Option>\n                  <Option value=\"romance\">言情</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"summary\"\n            label=\"项目简介\"\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入项目简介\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProjectList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,GAAG,QACE,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,cAAc,QACT,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAM;EAAEC;AAAM,CAAC,GAAGd,UAAU;AAC5B,MAAM;EAAEe;AAAO,CAAC,GAAGlB,MAAM;AACzB,MAAM;EAAEmB;AAAS,CAAC,GAAGpB,KAAK;AAE1B,MAAMqB,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0C,IAAI,CAAC,GAAGjC,IAAI,CAACkC,OAAO,CAAC,CAAC;EAI7B1C,SAAS,CAAC,MAAM;IACd2C,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMpB,UAAU,CAACqB,WAAW,CAAC,CAAC;MAC/C,MAAMC,YAAY,GAAGF,QAAQ,CAACG,IAAI,CAACd,QAAQ,IAAI,EAAE;;MAEjD;MACA,MAAMe,iBAAiB,GAAGF,YAAY,CAACG,GAAG,CAACC,OAAO;QAAA,IAAAC,iBAAA;QAAA,OAAK;UACrDC,EAAE,EAAEF,OAAO,CAACE,EAAE;UACdC,IAAI,EAAEH,OAAO,CAACG,IAAI;UAClBC,KAAK,EAAEJ,OAAO,CAACI,KAAK;UACpBC,MAAM,EAAEL,OAAO,CAACK,MAAM;UACtBC,IAAI,EAAEN,OAAO,CAACO,YAAY;UAC1BC,MAAM,EAAER,OAAO,CAACQ,MAAM;UACtBC,OAAO,EAAET,OAAO,CAACS,OAAO;UACxBC,SAAS,EAAEV,OAAO,CAACW,UAAU,IAAI,CAAC;UAClCC,YAAY,EAAEZ,OAAO,CAACa,aAAa,IAAI,CAAC;UACxCC,cAAc,EAAEd,OAAO,CAACe,eAAe,IAAI,CAAC;UAC5CC,QAAQ,EAAE,EAAAf,iBAAA,GAAAD,OAAO,CAACgB,QAAQ,cAAAf,iBAAA,uBAAhBA,iBAAA,CAAkBgB,qBAAqB,KAAI,CAAC;UACtDC,SAAS,EAAE,IAAIC,IAAI,CAACnB,OAAO,CAACoB,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAC5DC,SAAS,EAAE,IAAIH,IAAI,CAACnB,OAAO,CAACuB,UAAU,CAAC,CAACF,kBAAkB,CAAC,CAAC;UAC5DG,QAAQ,EAAExB,OAAO,CAACyB,SAAS,IAAI;QACjC,CAAC;MAAA,CAAC,CAAC;MAEHzC,WAAW,CAACc,iBAAiB,CAAC;IAChC,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCjE,OAAO,CAACiE,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0C,cAAc,GAAIpB,MAAM,IAAK;IACjC,MAAMqB,MAAM,GAAG;MACbC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOL,MAAM,CAACrB,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAM2B,aAAa,GAAI3B,MAAM,IAAK;IAChC,MAAM4B,KAAK,GAAG;MACZN,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAAC5B,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAM6B,WAAW,GAAI/B,IAAI,IAAK;IAC5B,MAAM8B,KAAK,GAAG;MACZE,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE;IACX,CAAC;IACD,OAAOR,KAAK,CAAC9B,IAAI,CAAC,IAAIA,IAAI;EAC5B,CAAC;EAED,MAAMuC,YAAY,GAAGA,CAAA,KAAM;IACzBvD,iBAAiB,CAAC,IAAI,CAAC;IACvBC,IAAI,CAACuD,WAAW,CAAC,CAAC;IAClB1D,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM2D,UAAU,GAAI/C,OAAO,IAAK;IAC9BV,iBAAiB,CAACU,OAAO,CAAC;IAC1BT,IAAI,CAACyD,cAAc,CAAChD,OAAO,CAAC;IAC5BZ,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM6D,YAAY,GAAG,MAAO/C,EAAE,IAAK;IACjC,IAAI;MACF;MACA,MAAM5B,UAAU,CAAC4E,aAAa,CAAChD,EAAE,CAAC;;MAElC;MACAzC,OAAO,CAAC0F,OAAO,CAAC,QAAQ,CAAC;MACzB1D,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOiC,KAAK,EAAE;MAAA,IAAA0B,eAAA,EAAAC,oBAAA;MACd1B,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BjE,OAAO,CAACiE,KAAK,CAAC,EAAA0B,eAAA,GAAA1B,KAAK,CAAChC,QAAQ,cAAA0D,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBvD,IAAI,cAAAwD,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,QAAQ,CAAC;IACzD;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MACF,IAAInE,cAAc,EAAE;QAClB;QACA,MAAMf,UAAU,CAACmF,aAAa,CAACpE,cAAc,CAACa,EAAE,EAAE;UAChD,GAAGsD,MAAM;UACTjD,YAAY,EAAEiD,MAAM,CAAClD;QACvB,CAAC,CAAC;QACF7C,OAAO,CAAC0F,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAM7E,UAAU,CAACoF,aAAa,CAAC;UAC7B,GAAGF,MAAM;UACTjD,YAAY,EAAEiD,MAAM,CAAClD;QACvB,CAAC,CAAC;QACF7C,OAAO,CAAC0F,OAAO,CAAC,QAAQ,CAAC;MAC3B;MACA/D,eAAe,CAAC,KAAK,CAAC;MACtBK,YAAY,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOiC,KAAK,EAAE;MAAA,IAAAiC,gBAAA,EAAAC,qBAAA;MACdjC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BjE,OAAO,CAACiE,KAAK,CAAC,EAAAiC,gBAAA,GAAAjC,KAAK,CAAChC,QAAQ,cAAAiE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9D,IAAI,cAAA+D,qBAAA,uBAApBA,qBAAA,CAAsBN,MAAM,KAAI,MAAM,CAAC;IACvD;EACF,CAAC;EAED,MAAMO,eAAe,GAAG,MAAO7D,OAAO,IAAK;IACzC,IAAI;MACF,MAAM1B,UAAU,CAACwF,gBAAgB,CAAC9D,OAAO,CAACE,EAAE,EAAE,GAAGF,OAAO,CAACG,IAAI,OAAO,CAAC;MACrE1C,OAAO,CAAC0F,OAAO,CAAC,QAAQ,CAAC;MACzB1D,YAAY,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOiC,KAAK,EAAE;MAAA,IAAAqC,gBAAA,EAAAC,qBAAA;MACdrC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BjE,OAAO,CAACiE,KAAK,CAAC,EAAAqC,gBAAA,GAAArC,KAAK,CAAChC,QAAQ,cAAAqE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlE,IAAI,cAAAmE,qBAAA,uBAApBA,qBAAA,CAAsBV,MAAM,KAAI,QAAQ,CAAC;IACzD;EACF,CAAC;EAED,MAAMW,OAAO,GAAG,CACd;IACE7D,KAAK,EAAE,MAAM;IACb8D,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB9F,OAAA;MAAA+F,QAAA,gBACE/F,OAAA;QAAKgG,KAAK,EAAE;UAAEC,UAAU,EAAE,MAAM;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAM,CAAE;QAAAL,QAAA,GACnFF,IAAI,EACJC,MAAM,CAAC9C,QAAQ,iBACdhD,OAAA,CAACrB,GAAG;UAAC0H,KAAK,EAAC,MAAM;UAACC,IAAI,EAAC,OAAO;UAAAP,QAAA,EAAC;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EACLZ,MAAM,CAAClE,KAAK,iBACX5B,OAAA;QAAKgG,KAAK,EAAE;UAAEW,QAAQ,EAAE,MAAM;UAAEN,KAAK,EAAE;QAAO,CAAE;QAAAN,QAAA,EAAED,MAAM,CAAClE;MAAK;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACrE;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,EACD;IACE9E,KAAK,EAAE,IAAI;IACX8D,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE;EACP,CAAC,EACD;IACE/D,KAAK,EAAE,IAAI;IACX8D,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAG9D,IAAI,iBAAK9B,OAAA,CAACrB,GAAG;MAAAoH,QAAA,EAAElC,WAAW,CAAC/B,IAAI;IAAC;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACjD,CAAC,EACD;IACE9E,KAAK,EAAE,IAAI;IACX8D,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAG5D,MAAM,iBACbhC,OAAA,CAACrB,GAAG;MAAC0H,KAAK,EAAEjD,cAAc,CAACpB,MAAM,CAAE;MAAA+D,QAAA,EAAEpC,aAAa,CAAC3B,MAAM;IAAC;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEpE,CAAC,EACD;IACE9E,KAAK,EAAE,IAAI;IACX8D,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGpD,QAAQ,iBACfxC,OAAA,CAACpB,QAAQ;MACPgI,OAAO,EAAEpE,QAAS;MAClB8D,IAAI,EAAC,OAAO;MACZtE,MAAM,EAAEQ,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG;IAAS;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD;EAEL,CAAC,EACD;IACE9E,KAAK,EAAE,IAAI;IACX+D,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACiB,CAAC,EAAEf,MAAM,kBAChB9F,OAAA;MAAKgG,KAAK,EAAE;QAAEW,QAAQ,EAAE;MAAO,CAAE;MAAAZ,QAAA,gBAC/B/F,OAAA;QAAA+F,QAAA,GAAK,gBAAI,EAAC,CAACD,MAAM,CAAC5D,SAAS,GAAG,KAAK,EAAE4E,OAAO,CAAC,CAAC,CAAC,EAAC,QAAC;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvD1G,OAAA;QAAA+F,QAAA,GAAK,gBAAI,EAACD,MAAM,CAAC1D,YAAY;MAAA;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpC1G,OAAA;QAAA+F,QAAA,GAAK,gBAAI,EAACD,MAAM,CAACxD,cAAc;MAAA;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC;EAET,CAAC,EACD;IACE9E,KAAK,EAAE,MAAM;IACb8D,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACE/D,KAAK,EAAE,IAAI;IACX+D,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACiB,CAAC,EAAEf,MAAM,kBAChB9F,OAAA,CAACtB,KAAK;MAAAqH,QAAA,gBACJ/F,OAAA,CAACvB,MAAM;QACLqD,IAAI,EAAC,MAAM;QACXiF,IAAI,eAAE/G,OAAA,CAACP,WAAW;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBM,OAAO,EAAEA,CAAA,KAAM1G,QAAQ,CAAC,aAAawF,MAAM,CAACpE,EAAE,EAAE,CAAE;QAAAqE,QAAA,EACnD;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1G,OAAA,CAACvB,MAAM;QACLqD,IAAI,EAAC,MAAM;QACXiF,IAAI,eAAE/G,OAAA,CAACT,YAAY;UAAAgH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBM,OAAO,EAAEA,CAAA,KAAMzC,UAAU,CAACuB,MAAM,CAAE;QAAAC,QAAA,EACnC;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1G,OAAA,CAACvB,MAAM;QACLqD,IAAI,EAAC,MAAM;QACXiF,IAAI,eAAE/G,OAAA,CAACN,YAAY;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBM,OAAO,EAAEA,CAAA,KAAM3B,eAAe,CAACS,MAAM,CAAE;QAAAC,QAAA,EACxC;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1G,OAAA,CAACd,UAAU;QACT0C,KAAK,EAAC,oEAAa;QACnBqF,SAAS,EAAEA,CAAA,KAAMxC,YAAY,CAACqB,MAAM,CAACpE,EAAE,CAAE;QACzCwF,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAApB,QAAA,eAEf/F,OAAA,CAACvB,MAAM;UAACqD,IAAI,EAAC,MAAM;UAACsF,MAAM;UAACL,IAAI,eAAE/G,OAAA,CAACR,cAAc;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAX,QAAA,EAAC;QAErD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE1G,OAAA;IAAKqH,SAAS,EAAC,SAAS;IAAAtB,QAAA,gBACtB/F,OAAA;MAAKqH,SAAS,EAAC,aAAa;MAAAtB,QAAA,eAC1B/F,OAAA,CAACC,KAAK;QAACqH,KAAK,EAAE,CAAE;QAACD,SAAS,EAAC,YAAY;QAAAtB,QAAA,EAAC;MAAI;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAEN1G,OAAA,CAACzB,IAAI;MAAAwH,QAAA,gBACH/F,OAAA;QAAKqH,SAAS,EAAC,SAAS;QAAAtB,QAAA,gBACtB/F,OAAA;UAAKqH,SAAS,EAAC,cAAc;UAAAtB,QAAA,eAC3B/F,OAAA,CAACvB,MAAM;YACLqD,IAAI,EAAC,SAAS;YACdiF,IAAI,eAAE/G,OAAA,CAACV,YAAY;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBM,OAAO,EAAE3C,YAAa;YAAA0B,QAAA,EACvB;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1G,OAAA;UAAKqH,SAAS,EAAC,eAAe;UAAAtB,QAAA,eAC5B/F,OAAA,CAACtB,KAAK;YAAAqH,QAAA,gBACJ/F,OAAA,CAACvB,MAAM;cAACsI,IAAI,eAAE/G,OAAA,CAACJ,cAAc;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAX,QAAA,EAAC;YAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/C1G,OAAA,CAACvB,MAAM;cAACsI,IAAI,eAAE/G,OAAA,CAACL,cAAc;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAX,QAAA,EAAC;YAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1G,OAAA,CAACxB,KAAK;QACJiH,OAAO,EAAEA,OAAQ;QACjB8B,UAAU,EAAEhH,QAAS;QACrBiH,MAAM,EAAC,IAAI;QACX/G,OAAO,EAAEA,OAAQ;QACjBgH,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEP1G,OAAA,CAACnB,KAAK;MACJ+C,KAAK,EAAEf,cAAc,GAAG,MAAM,GAAG,MAAO;MACxCiH,IAAI,EAAEnH,YAAa;MACnBoH,QAAQ,EAAEA,CAAA,KAAMnH,eAAe,CAAC,KAAK,CAAE;MACvCoH,IAAI,EAAEA,CAAA,KAAMjH,IAAI,CAACkH,MAAM,CAAC,CAAE;MAC1BC,KAAK,EAAE,GAAI;MAAAnC,QAAA,eAEX/F,OAAA,CAAClB,IAAI;QACHiC,IAAI,EAAEA,IAAK;QACXoH,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAErD,YAAa;QAAAgB,QAAA,gBAEvB/F,OAAA,CAACZ,GAAG;UAACiJ,MAAM,EAAE,EAAG;UAAAtC,QAAA,gBACd/F,OAAA,CAACX,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAvC,QAAA,eACZ/F,OAAA,CAAClB,IAAI,CAACyJ,IAAI;cACR5G,IAAI,EAAC,MAAM;cACX6G,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzJ,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA8G,QAAA,eAEhD/F,OAAA,CAACjB,KAAK;gBAAC4J,WAAW,EAAC;cAAS;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1G,OAAA,CAACX,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAvC,QAAA,eACZ/F,OAAA,CAAClB,IAAI,CAACyJ,IAAI;cACR5G,IAAI,EAAC,OAAO;cACZ6G,KAAK,EAAC,0BAAM;cAAAzC,QAAA,eAEZ/F,OAAA,CAACjB,KAAK;gBAAC4J,WAAW,EAAC;cAAS;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1G,OAAA,CAACZ,GAAG;UAACiJ,MAAM,EAAE,EAAG;UAAAtC,QAAA,gBACd/F,OAAA,CAACX,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAvC,QAAA,eACZ/F,OAAA,CAAClB,IAAI,CAACyJ,IAAI;cACR5G,IAAI,EAAC,QAAQ;cACb6G,KAAK,EAAC,cAAI;cAAAzC,QAAA,eAEV/F,OAAA,CAACjB,KAAK;gBAAC4J,WAAW,EAAC;cAAS;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1G,OAAA,CAACX,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAvC,QAAA,eACZ/F,OAAA,CAAClB,IAAI,CAACyJ,IAAI;cACR5G,IAAI,EAAC,MAAM;cACX6G,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzJ,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA8G,QAAA,eAEhD/F,OAAA,CAAChB,MAAM;gBAAC2J,WAAW,EAAC,4CAAS;gBAAA5C,QAAA,gBAC3B/F,OAAA,CAACE,MAAM;kBAAC0I,KAAK,EAAC,SAAS;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC1G,OAAA,CAACE,MAAM;kBAAC0I,KAAK,EAAC,SAAS;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC1G,OAAA,CAACE,MAAM;kBAAC0I,KAAK,EAAC,OAAO;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjC1G,OAAA,CAACE,MAAM;kBAAC0I,KAAK,EAAC,OAAO;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjC1G,OAAA,CAACE,MAAM;kBAAC0I,KAAK,EAAC,QAAQ;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC1G,OAAA,CAACE,MAAM;kBAAC0I,KAAK,EAAC,YAAY;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC1G,OAAA,CAACE,MAAM;kBAAC0I,KAAK,EAAC,SAAS;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1G,OAAA,CAAClB,IAAI,CAACyJ,IAAI;UACR5G,IAAI,EAAC,SAAS;UACd6G,KAAK,EAAC,0BAAM;UAAAzC,QAAA,eAEZ/F,OAAA,CAACG,QAAQ;YACP0I,IAAI,EAAE,CAAE;YACRF,WAAW,EAAC;UAAS;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACrG,EAAA,CA1WID,WAAW;EAAA,QACEP,WAAW,EAKbf,IAAI,CAACkC,OAAO;AAAA;AAAA8H,EAAA,GANvB1I,WAAW;AA4WjB,eAAeA,WAAW;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}