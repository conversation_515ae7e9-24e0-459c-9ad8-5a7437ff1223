{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { DashboardOutlined, ProjectOutlined, UserOutlined, BookOutlined, RobotOutlined, SettingOutlined, MenuFoldOutlined, MenuUnfoldOutlined, LogoutOutlined, BellOutlined, FolderOutlined, DatabaseOutlined, ControlOutlined, EyeOutlined } from '@ant-design/icons';\nimport { projectAPI } from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const [projects, setProjects] = useState([]);\n  const [, setLoading] = useState(false);\n  const [openKeys, setOpenKeys] = useState(['project-management']);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取当前项目ID（如果在项目页面中）\n  const getProjectId = () => {\n    const pathParts = location.pathname.split('/');\n    if (pathParts[1] === 'projects' && pathParts[2]) {\n      return pathParts[2];\n    }\n    return null;\n  };\n  const projectId = getProjectId();\n\n  // 加载项目列表\n  useEffect(() => {\n    loadProjects();\n  }, []);\n\n  // 监听路径变化，如果在项目页面则重新加载项目列表\n  useEffect(() => {\n    if (location.pathname.includes('/projects')) {\n      loadProjects();\n    }\n  }, [location.pathname]);\n\n  // 监听路径变化，更新菜单展开状态\n  useEffect(() => {\n    const pathname = location.pathname;\n    const newOpenKeys = ['project-management'];\n\n    // 如果在项目页面，展开对应的项目菜单\n    if (pathname.startsWith('/projects/') && projectId) {\n      newOpenKeys.push(`project-${projectId}`);\n\n      // 如果在工具相关页面，展开工具菜单\n      if (pathname.includes('/tools') || pathname.includes('/ai-assistant') || pathname.includes('/ai-test') || pathname.includes('/settings')) {\n        newOpenKeys.push(`project-${projectId}-tools`);\n      }\n    }\n    setOpenKeys(newOpenKeys);\n  }, [location.pathname, projectId]);\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      const response = await projectAPI.getProjects();\n      const projectsData = response.data.projects || [];\n      setProjects(projectsData);\n    } catch (error) {\n      console.error('加载项目列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 构建项目子菜单\n  const buildProjectSubMenu = project => [{\n    key: `/projects/${project.id}`,\n    icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 13\n    }, this),\n    label: '项目概览'\n  }, {\n    key: `/projects/${project.id}/volumes`,\n    icon: /*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 13\n    }, this),\n    label: '卷宗管理'\n  }, {\n    key: `/projects/${project.id}/content`,\n    icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this),\n    label: '内容管理'\n  }, {\n    key: `/projects/${project.id}/settings`,\n    icon: /*#__PURE__*/_jsxDEV(ControlOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 13\n    }, this),\n    label: '设定管理'\n  }, {\n    key: `project-${project.id}-tools`,\n    icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this),\n    label: '工具',\n    children: [{\n      key: `/ai-assistant?project=${project.id}`,\n      icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 17\n      }, this),\n      label: 'Agent-AI助手'\n    }, {\n      key: `/ai-test?project=${project.id}`,\n      icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 17\n      }, this),\n      label: 'Agent-AI测试'\n    }, {\n      key: `/settings?project=${project.id}`,\n      icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 17\n      }, this),\n      label: '系统设置'\n    }]\n  }];\n\n  // 主菜单项\n  const mainMenuItems = [{\n    key: '/',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 13\n    }, this),\n    label: '仪表盘'\n  }, {\n    key: 'project-management',\n    icon: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 13\n    }, this),\n    label: '项目管理',\n    children: [{\n      key: '/projects',\n      icon: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 17\n      }, this),\n      label: '项目列表'\n    }, ...projects.map(project => ({\n      key: `project-${project.id}`,\n      icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 17\n      }, this),\n      label: project.name,\n      children: buildProjectSubMenu(project)\n    }))]\n  }];\n\n  // 获取当前选中的菜单key\n  const getSelectedKeys = () => {\n    const pathname = location.pathname;\n\n    // 如果是项目相关页面，返回具体的路径\n    if (pathname.startsWith('/projects/')) {\n      return [pathname];\n    }\n\n    // 其他页面直接返回路径\n    return [pathname];\n  };\n\n  // 获取默认展开的菜单key\n  const getDefaultOpenKeys = () => {\n    const pathname = location.pathname;\n    const openKeys = ['project-management'];\n\n    // 如果在项目页面，展开对应的项目菜单\n    if (pathname.startsWith('/projects/') && projectId) {\n      openKeys.push(`project-${projectId}`);\n    }\n    return openKeys;\n  };\n\n  // 用户菜单\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 13\n    }, this),\n    label: '个人资料'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 13\n    }, this),\n    label: '偏好设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 13\n    }, this),\n    label: '退出登录'\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    // 如果是项目管理或项目分组的key，不进行导航\n    if (key === 'project-management' || key.startsWith('project-')) {\n      return;\n    }\n    navigate(key);\n  };\n  const handleUserMenuClick = ({\n    key\n  }) => {\n    switch (key) {\n      case 'profile':\n        navigate('/profile');\n        break;\n      case 'settings':\n        navigate('/settings');\n        break;\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      default:\n        break;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    className: \"layout-container\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      className: \"layout-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 58\n            }, this),\n            onClick: () => setCollapsed(!collapsed),\n            style: {\n              marginRight: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              color: '#1890ff',\n              fontSize: '20px',\n              fontWeight: 'bold'\n            },\n            children: \"NovelCraft\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 39\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems,\n              onClick: handleUserMenuClick\n            },\n            placement: \"bottomRight\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u7528\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      className: \"layout-content\",\n      children: [/*#__PURE__*/_jsxDEV(Sider, {\n        className: \"layout-sider\",\n        collapsed: collapsed,\n        width: 240,\n        collapsedWidth: 80,\n        theme: \"light\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '100%',\n            overflowY: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(Menu, {\n            mode: \"inline\",\n            selectedKeys: getSelectedKeys(),\n            items: mainMenuItems,\n            onClick: handleMenuClick,\n            style: {\n              borderRight: 0\n            },\n            defaultOpenKeys: getDefaultOpenKeys(),\n            openKeys: getDefaultOpenKeys()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        className: \"layout-main\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 239,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"wIJGuPzlqQqv9iO/wtD4oF/j1RU=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "AntLayout", "<PERSON><PERSON>", "Avatar", "Dropdown", "<PERSON><PERSON>", "Space", "useNavigate", "useLocation", "DashboardOutlined", "ProjectOutlined", "UserOutlined", "BookOutlined", "RobotOutlined", "SettingOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "LogoutOutlined", "BellOutlined", "FolderOutlined", "DatabaseOutlined", "ControlOutlined", "EyeOutlined", "projectAPI", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "children", "_s", "collapsed", "setCollapsed", "projects", "setProjects", "setLoading", "openKeys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "navigate", "location", "getProjectId", "pathParts", "pathname", "split", "projectId", "loadProjects", "includes", "newOpenKeys", "startsWith", "push", "response", "getProjects", "projectsData", "data", "error", "console", "buildProjectSubMenu", "project", "key", "id", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "mainMenuItems", "map", "name", "getSelectedKeys", "getDefaultOpenKeys", "userMenuItems", "type", "handleMenuClick", "handleUserMenuClick", "log", "className", "style", "display", "alignItems", "justifyContent", "onClick", "marginRight", "margin", "color", "fontSize", "fontWeight", "menu", "items", "placement", "cursor", "width", "collapsedWidth", "theme", "height", "overflowY", "mode", "<PERSON><PERSON><PERSON><PERSON>", "borderRight", "defaultOpenKeys", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/components/Layout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  DashboardOutlined,\n  ProjectOutlined,\n  UserOutlined,\n  BookOutlined,\n  RobotOutlined,\n  SettingOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  LogoutOutlined,\n  BellOutlined,\n  FolderOutlined,\n  DatabaseOutlined,\n  ControlOutlined,\n  EyeOutlined\n} from '@ant-design/icons';\nimport { projectAPI } from '../utils/api';\n\nconst { Header, Sider, Content } = AntLayout;\n\nconst Layout = ({ children }) => {\n  const [collapsed, setCollapsed] = useState(false);\n  const [projects, setProjects] = useState([]);\n  const [, setLoading] = useState(false);\n  const [openKeys, setOpenKeys] = useState(['project-management']);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取当前项目ID（如果在项目页面中）\n  const getProjectId = () => {\n    const pathParts = location.pathname.split('/');\n    if (pathParts[1] === 'projects' && pathParts[2]) {\n      return pathParts[2];\n    }\n    return null;\n  };\n\n  const projectId = getProjectId();\n\n  // 加载项目列表\n  useEffect(() => {\n    loadProjects();\n  }, []);\n\n  // 监听路径变化，如果在项目页面则重新加载项目列表\n  useEffect(() => {\n    if (location.pathname.includes('/projects')) {\n      loadProjects();\n    }\n  }, [location.pathname]);\n\n  // 监听路径变化，更新菜单展开状态\n  useEffect(() => {\n    const pathname = location.pathname;\n    const newOpenKeys = ['project-management'];\n\n    // 如果在项目页面，展开对应的项目菜单\n    if (pathname.startsWith('/projects/') && projectId) {\n      newOpenKeys.push(`project-${projectId}`);\n\n      // 如果在工具相关页面，展开工具菜单\n      if (pathname.includes('/tools') ||\n          pathname.includes('/ai-assistant') ||\n          pathname.includes('/ai-test') ||\n          pathname.includes('/settings')) {\n        newOpenKeys.push(`project-${projectId}-tools`);\n      }\n    }\n\n    setOpenKeys(newOpenKeys);\n  }, [location.pathname, projectId]);\n\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      const response = await projectAPI.getProjects();\n      const projectsData = response.data.projects || [];\n\n      setProjects(projectsData);\n    } catch (error) {\n      console.error('加载项目列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 构建项目子菜单\n  const buildProjectSubMenu = (project) => [\n    {\n      key: `/projects/${project.id}`,\n      icon: <EyeOutlined />,\n      label: '项目概览',\n    },\n    {\n      key: `/projects/${project.id}/volumes`,\n      icon: <FolderOutlined />,\n      label: '卷宗管理',\n    },\n    {\n      key: `/projects/${project.id}/content`,\n      icon: <DatabaseOutlined />,\n      label: '内容管理',\n    },\n    {\n      key: `/projects/${project.id}/settings`,\n      icon: <ControlOutlined />,\n      label: '设定管理',\n    },\n    {\n      key: `project-${project.id}-tools`,\n      icon: <RobotOutlined />,\n      label: '工具',\n      children: [\n        {\n          key: `/ai-assistant?project=${project.id}`,\n          icon: <RobotOutlined />,\n          label: 'Agent-AI助手',\n        },\n        {\n          key: `/ai-test?project=${project.id}`,\n          icon: <RobotOutlined />,\n          label: 'Agent-AI测试',\n        },\n        {\n          key: `/settings?project=${project.id}`,\n          icon: <SettingOutlined />,\n          label: '系统设置',\n        },\n      ],\n    },\n  ];\n\n  // 主菜单项\n  const mainMenuItems = [\n    {\n      key: '/',\n      icon: <DashboardOutlined />,\n      label: '仪表盘',\n    },\n    {\n      key: 'project-management',\n      icon: <ProjectOutlined />,\n      label: '项目管理',\n      children: [\n        {\n          key: '/projects',\n          icon: <ProjectOutlined />,\n          label: '项目列表',\n        },\n        ...projects.map(project => ({\n          key: `project-${project.id}`,\n          icon: <BookOutlined />,\n          label: project.name,\n          children: buildProjectSubMenu(project),\n        })),\n      ],\n    },\n  ];\n\n\n\n  // 获取当前选中的菜单key\n  const getSelectedKeys = () => {\n    const pathname = location.pathname;\n\n    // 如果是项目相关页面，返回具体的路径\n    if (pathname.startsWith('/projects/')) {\n      return [pathname];\n    }\n\n    // 其他页面直接返回路径\n    return [pathname];\n  };\n\n  // 获取默认展开的菜单key\n  const getDefaultOpenKeys = () => {\n    const pathname = location.pathname;\n    const openKeys = ['project-management'];\n\n    // 如果在项目页面，展开对应的项目菜单\n    if (pathname.startsWith('/projects/') && projectId) {\n      openKeys.push(`project-${projectId}`);\n    }\n\n    return openKeys;\n  };\n\n  // 用户菜单\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '偏好设置',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n    },\n  ];\n\n  const handleMenuClick = ({ key }) => {\n    // 如果是项目管理或项目分组的key，不进行导航\n    if (key === 'project-management' || key.startsWith('project-')) {\n      return;\n    }\n    navigate(key);\n  };\n\n  const handleUserMenuClick = ({ key }) => {\n    switch (key) {\n      case 'profile':\n        navigate('/profile');\n        break;\n      case 'settings':\n        navigate('/settings');\n        break;\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      default:\n        break;\n    }\n  };\n\n  return (\n    <AntLayout className=\"layout-container\">\n      <Header className=\"layout-header\">\n        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <div style={{ display: 'flex', alignItems: 'center' }}>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={() => setCollapsed(!collapsed)}\n              style={{ marginRight: 16 }}\n            />\n            <h1 style={{ margin: 0, color: '#1890ff', fontSize: '20px', fontWeight: 'bold' }}>\n              NovelCraft\n            </h1>\n          </div>\n\n          <Space>\n            <Button type=\"text\" icon={<BellOutlined />} />\n            <Dropdown\n              menu={{\n                items: userMenuItems,\n                onClick: handleUserMenuClick,\n              }}\n              placement=\"bottomRight\"\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <span>用户</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </div>\n      </Header>\n\n      <AntLayout className=\"layout-content\">\n        <Sider\n          className=\"layout-sider\"\n          collapsed={collapsed}\n          width={240}\n          collapsedWidth={80}\n          theme=\"light\"\n        >\n          <div style={{ height: '100%', overflowY: 'auto' }}>\n            {/* 主菜单 */}\n            <Menu\n              mode=\"inline\"\n              selectedKeys={getSelectedKeys()}\n              items={mainMenuItems}\n              onClick={handleMenuClick}\n              style={{ borderRight: 0 }}\n              defaultOpenKeys={getDefaultOpenKeys()}\n              openKeys={getDefaultOpenKeys()}\n            />\n          </div>\n        </Sider>\n\n        <Content className=\"layout-main\">\n          {children}\n        </Content>\n      </AntLayout>\n    </AntLayout>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,IAAIC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AACjF,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,eAAe,EACfC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,gBAAgB,EAChBC,eAAe,EACfC,WAAW,QACN,mBAAmB;AAC1B,SAASC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAG3B,SAAS;AAE5C,MAAMD,MAAM,GAAGA,CAAC;EAAE6B;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,GAAGqC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACtC,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAChE,MAAMwC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMgC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAGF,QAAQ,CAACG,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC;IAC9C,IAAIF,SAAS,CAAC,CAAC,CAAC,KAAK,UAAU,IAAIA,SAAS,CAAC,CAAC,CAAC,EAAE;MAC/C,OAAOA,SAAS,CAAC,CAAC,CAAC;IACrB;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMG,SAAS,GAAGJ,YAAY,CAAC,CAAC;;EAEhC;EACAzC,SAAS,CAAC,MAAM;IACd8C,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9C,SAAS,CAAC,MAAM;IACd,IAAIwC,QAAQ,CAACG,QAAQ,CAACI,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC3CD,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACN,QAAQ,CAACG,QAAQ,CAAC,CAAC;;EAEvB;EACA3C,SAAS,CAAC,MAAM;IACd,MAAM2C,QAAQ,GAAGH,QAAQ,CAACG,QAAQ;IAClC,MAAMK,WAAW,GAAG,CAAC,oBAAoB,CAAC;;IAE1C;IACA,IAAIL,QAAQ,CAACM,UAAU,CAAC,YAAY,CAAC,IAAIJ,SAAS,EAAE;MAClDG,WAAW,CAACE,IAAI,CAAC,WAAWL,SAAS,EAAE,CAAC;;MAExC;MACA,IAAIF,QAAQ,CAACI,QAAQ,CAAC,QAAQ,CAAC,IAC3BJ,QAAQ,CAACI,QAAQ,CAAC,eAAe,CAAC,IAClCJ,QAAQ,CAACI,QAAQ,CAAC,UAAU,CAAC,IAC7BJ,QAAQ,CAACI,QAAQ,CAAC,WAAW,CAAC,EAAE;QAClCC,WAAW,CAACE,IAAI,CAAC,WAAWL,SAAS,QAAQ,CAAC;MAChD;IACF;IAEAP,WAAW,CAACU,WAAW,CAAC;EAC1B,CAAC,EAAE,CAACR,QAAQ,CAACG,QAAQ,EAAEE,SAAS,CAAC,CAAC;EAElC,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BV,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAM3B,UAAU,CAAC4B,WAAW,CAAC,CAAC;MAC/C,MAAMC,YAAY,GAAGF,QAAQ,CAACG,IAAI,CAACpB,QAAQ,IAAI,EAAE;MAEjDC,WAAW,CAACkB,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqB,mBAAmB,GAAIC,OAAO,IAAK,CACvC;IACEC,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,EAAE;IAC9BC,IAAI,eAAEnC,OAAA,CAACH,WAAW;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,UAAU;IACtCC,IAAI,eAAEnC,OAAA,CAACN,cAAc;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,UAAU;IACtCC,IAAI,eAAEnC,OAAA,CAACL,gBAAgB;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,aAAaD,OAAO,CAACE,EAAE,WAAW;IACvCC,IAAI,eAAEnC,OAAA,CAACJ,eAAe;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,WAAWD,OAAO,CAACE,EAAE,QAAQ;IAClCC,IAAI,eAAEnC,OAAA,CAACZ,aAAa;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,IAAI;IACXpC,QAAQ,EAAE,CACR;MACE6B,GAAG,EAAE,yBAAyBD,OAAO,CAACE,EAAE,EAAE;MAC1CC,IAAI,eAAEnC,OAAA,CAACZ,aAAa;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,KAAK,EAAE;IACT,CAAC,EACD;MACEP,GAAG,EAAE,oBAAoBD,OAAO,CAACE,EAAE,EAAE;MACrCC,IAAI,eAAEnC,OAAA,CAACZ,aAAa;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,KAAK,EAAE;IACT,CAAC,EACD;MACEP,GAAG,EAAE,qBAAqBD,OAAO,CAACE,EAAE,EAAE;MACtCC,IAAI,eAAEnC,OAAA,CAACX,eAAe;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzBC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,CACF;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IACER,GAAG,EAAE,GAAG;IACRE,IAAI,eAAEnC,OAAA,CAAChB,iBAAiB;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,oBAAoB;IACzBE,IAAI,eAAEnC,OAAA,CAACf,eAAe;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACbpC,QAAQ,EAAE,CACR;MACE6B,GAAG,EAAE,WAAW;MAChBE,IAAI,eAAEnC,OAAA,CAACf,eAAe;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzBC,KAAK,EAAE;IACT,CAAC,EACD,GAAGhC,QAAQ,CAACkC,GAAG,CAACV,OAAO,KAAK;MAC1BC,GAAG,EAAE,WAAWD,OAAO,CAACE,EAAE,EAAE;MAC5BC,IAAI,eAAEnC,OAAA,CAACb,YAAY;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,KAAK,EAAER,OAAO,CAACW,IAAI;MACnBvC,QAAQ,EAAE2B,mBAAmB,CAACC,OAAO;IACvC,CAAC,CAAC,CAAC;EAEP,CAAC,CACF;;EAID;EACA,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAM3B,QAAQ,GAAGH,QAAQ,CAACG,QAAQ;;IAElC;IACA,IAAIA,QAAQ,CAACM,UAAU,CAAC,YAAY,CAAC,EAAE;MACrC,OAAO,CAACN,QAAQ,CAAC;IACnB;;IAEA;IACA,OAAO,CAACA,QAAQ,CAAC;EACnB,CAAC;;EAED;EACA,MAAM4B,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAM5B,QAAQ,GAAGH,QAAQ,CAACG,QAAQ;IAClC,MAAMN,QAAQ,GAAG,CAAC,oBAAoB,CAAC;;IAEvC;IACA,IAAIM,QAAQ,CAACM,UAAU,CAAC,YAAY,CAAC,IAAIJ,SAAS,EAAE;MAClDR,QAAQ,CAACa,IAAI,CAAC,WAAWL,SAAS,EAAE,CAAC;IACvC;IAEA,OAAOR,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMmC,aAAa,GAAG,CACpB;IACEb,GAAG,EAAE,SAAS;IACdE,IAAI,eAAEnC,OAAA,CAACd,YAAY;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,UAAU;IACfE,IAAI,eAAEnC,OAAA,CAACX,eAAe;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEO,IAAI,EAAE;EACR,CAAC,EACD;IACEd,GAAG,EAAE,QAAQ;IACbE,IAAI,eAAEnC,OAAA,CAACR,cAAc;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMQ,eAAe,GAAGA,CAAC;IAAEf;EAAI,CAAC,KAAK;IACnC;IACA,IAAIA,GAAG,KAAK,oBAAoB,IAAIA,GAAG,CAACV,UAAU,CAAC,UAAU,CAAC,EAAE;MAC9D;IACF;IACAV,QAAQ,CAACoB,GAAG,CAAC;EACf,CAAC;EAED,MAAMgB,mBAAmB,GAAGA,CAAC;IAAEhB;EAAI,CAAC,KAAK;IACvC,QAAQA,GAAG;MACT,KAAK,SAAS;QACZpB,QAAQ,CAAC,UAAU,CAAC;QACpB;MACF,KAAK,UAAU;QACbA,QAAQ,CAAC,WAAW,CAAC;QACrB;MACF,KAAK,QAAQ;QACX;QACAiB,OAAO,CAACoB,GAAG,CAAC,MAAM,CAAC;QACnB;MACF;QACE;IACJ;EACF,CAAC;EAED,oBACElD,OAAA,CAACxB,SAAS;IAAC2E,SAAS,EAAC,kBAAkB;IAAA/C,QAAA,gBACrCJ,OAAA,CAACC,MAAM;MAACkD,SAAS,EAAC,eAAe;MAAA/C,QAAA,eAC/BJ,OAAA;QAAKoD,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAnD,QAAA,gBACrFJ,OAAA;UAAKoD,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAlD,QAAA,gBACpDJ,OAAA,CAACpB,MAAM;YACLmE,IAAI,EAAC,MAAM;YACXZ,IAAI,EAAE7B,SAAS,gBAAGN,OAAA,CAACT,kBAAkB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGvC,OAAA,CAACV,gBAAgB;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChEiB,OAAO,EAAEA,CAAA,KAAMjD,YAAY,CAAC,CAACD,SAAS,CAAE;YACxC8C,KAAK,EAAE;cAAEK,WAAW,EAAE;YAAG;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACFvC,OAAA;YAAIoD,KAAK,EAAE;cAAEM,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAAzD,QAAA,EAAC;UAElF;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENvC,OAAA,CAACnB,KAAK;UAAAuB,QAAA,gBACJJ,OAAA,CAACpB,MAAM;YAACmE,IAAI,EAAC,MAAM;YAACZ,IAAI,eAAEnC,OAAA,CAACP,YAAY;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9CvC,OAAA,CAACrB,QAAQ;YACPmF,IAAI,EAAE;cACJC,KAAK,EAAEjB,aAAa;cACpBU,OAAO,EAAEP;YACX,CAAE;YACFe,SAAS,EAAC,aAAa;YAAA5D,QAAA,eAEvBJ,OAAA,CAACnB,KAAK;cAACuE,KAAK,EAAE;gBAAEa,MAAM,EAAE;cAAU,CAAE;cAAA7D,QAAA,gBAClCJ,OAAA,CAACtB,MAAM;gBAACyD,IAAI,eAAEnC,OAAA,CAACd,YAAY;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCvC,OAAA;gBAAAI,QAAA,EAAM;cAAE;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETvC,OAAA,CAACxB,SAAS;MAAC2E,SAAS,EAAC,gBAAgB;MAAA/C,QAAA,gBACnCJ,OAAA,CAACE,KAAK;QACJiD,SAAS,EAAC,cAAc;QACxB7C,SAAS,EAAEA,SAAU;QACrB4D,KAAK,EAAE,GAAI;QACXC,cAAc,EAAE,EAAG;QACnBC,KAAK,EAAC,OAAO;QAAAhE,QAAA,eAEbJ,OAAA;UAAKoD,KAAK,EAAE;YAAEiB,MAAM,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAlE,QAAA,eAEhDJ,OAAA,CAACvB,IAAI;YACH8F,IAAI,EAAC,QAAQ;YACbC,YAAY,EAAE5B,eAAe,CAAC,CAAE;YAChCmB,KAAK,EAAEtB,aAAc;YACrBe,OAAO,EAAER,eAAgB;YACzBI,KAAK,EAAE;cAAEqB,WAAW,EAAE;YAAE,CAAE;YAC1BC,eAAe,EAAE7B,kBAAkB,CAAC,CAAE;YACtClC,QAAQ,EAAEkC,kBAAkB,CAAC;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERvC,OAAA,CAACG,OAAO;QAACgD,SAAS,EAAC,aAAa;QAAA/C,QAAA,EAC7BA;MAAQ;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAAClC,EAAA,CApRI9B,MAAM;EAAA,QAKOO,WAAW,EACXC,WAAW;AAAA;AAAA4F,EAAA,GANxBpG,MAAM;AAsRZ,eAAeA,MAAM;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}