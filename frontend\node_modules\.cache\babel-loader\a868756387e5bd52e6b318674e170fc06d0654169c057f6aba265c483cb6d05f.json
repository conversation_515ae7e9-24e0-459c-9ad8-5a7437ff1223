{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { DashboardOutlined, ProjectOutlined, UserOutlined, BookOutlined, FileTextOutlined, RobotOutlined, SettingOutlined, MenuFoldOutlined, MenuUnfoldOutlined, LogoutOutlined, BellOutlined, FolderOutlined, DatabaseOutlined, ControlOutlined, EyeOutlined } from '@ant-design/icons';\nimport { projectAPI } from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取当前项目ID（如果在项目页面中）\n  const getProjectId = () => {\n    const pathParts = location.pathname.split('/');\n    if (pathParts[1] === 'projects' && pathParts[2]) {\n      return pathParts[2];\n    }\n    return null;\n  };\n  const projectId = getProjectId();\n\n  // 加载项目列表\n  useEffect(() => {\n    loadProjects();\n  }, []);\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      const response = await projectAPI.getProjects();\n      const projectsData = response.data.projects || [];\n      setProjects(projectsData);\n    } catch (error) {\n      console.error('加载项目列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 基础菜单项\n  const baseMenuItems = [{\n    key: '/',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this),\n    label: '仪表盘'\n  }, {\n    key: '/projects',\n    icon: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 13\n    }, this),\n    label: '项目管理'\n  }];\n\n  // 项目管理子菜单项\n  const projectSubMenuItems = projectId ? [{\n    key: `/projects/${projectId}/volumes`,\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this),\n    label: '卷宗管理'\n  }, {\n    key: `/projects/${projectId}/content`,\n    icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this),\n    label: '内容管理'\n  }, {\n    key: `/projects/${projectId}/settings`,\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 13\n    }, this),\n    label: '设定管理'\n  }] : [];\n\n  // 工具菜单项\n  const toolsMenuItems = [{\n    key: '/ai-assistant',\n    icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 13\n    }, this),\n    label: 'Agent-AI助手'\n  }, {\n    key: '/ai-test',\n    icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this),\n    label: 'Agent-AI测试'\n  }, {\n    key: '/settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 13\n    }, this),\n    label: '系统设置'\n  }];\n\n  // 用户菜单\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 13\n    }, this),\n    label: '个人资料'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 13\n    }, this),\n    label: '偏好设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 13\n    }, this),\n    label: '退出登录'\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    navigate(key);\n  };\n  const handleUserMenuClick = ({\n    key\n  }) => {\n    switch (key) {\n      case 'profile':\n        navigate('/profile');\n        break;\n      case 'settings':\n        navigate('/settings');\n        break;\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      default:\n        break;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    className: \"layout-container\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      className: \"layout-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 58\n            }, this),\n            onClick: () => setCollapsed(!collapsed),\n            style: {\n              marginRight: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              color: '#1890ff',\n              fontSize: '20px',\n              fontWeight: 'bold'\n            },\n            children: \"NovelCraft\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 39\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems,\n              onClick: handleUserMenuClick\n            },\n            placement: \"bottomRight\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u7528\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      className: \"layout-content\",\n      children: [/*#__PURE__*/_jsxDEV(Sider, {\n        className: \"layout-sider\",\n        collapsed: collapsed,\n        width: 240,\n        collapsedWidth: 80,\n        theme: \"light\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '100%',\n            overflowY: 'auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Menu, {\n            mode: \"inline\",\n            selectedKeys: [location.pathname],\n            items: baseMenuItems,\n            onClick: handleMenuClick,\n            style: {\n              borderRight: 0,\n              marginBottom: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), projectId && /*#__PURE__*/_jsxDEV(Menu, {\n            mode: \"inline\",\n            selectedKeys: [location.pathname],\n            items: projectSubMenuItems,\n            onClick: handleMenuClick,\n            style: {\n              borderRight: 0,\n              marginBottom: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tools-divider\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tools-title\",\n              children: \"\\u5DE5\\u5177\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Menu, {\n              mode: \"inline\",\n              selectedKeys: [location.pathname],\n              items: toolsMenuItems,\n              onClick: handleMenuClick,\n              style: {\n                borderRight: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        className: \"layout-main\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"U2uVLKJEKesVhGwcvCnkYn43IhU=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "AntLayout", "<PERSON><PERSON>", "Avatar", "Dropdown", "<PERSON><PERSON>", "Space", "useNavigate", "useLocation", "DashboardOutlined", "ProjectOutlined", "UserOutlined", "BookOutlined", "FileTextOutlined", "RobotOutlined", "SettingOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "LogoutOutlined", "BellOutlined", "FolderOutlined", "DatabaseOutlined", "ControlOutlined", "EyeOutlined", "projectAPI", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "children", "_s", "collapsed", "setCollapsed", "projects", "setProjects", "loading", "setLoading", "navigate", "location", "getProjectId", "pathParts", "pathname", "split", "projectId", "loadProjects", "response", "getProjects", "projectsData", "data", "error", "console", "baseMenuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "projectSubMenuItems", "toolsMenuItems", "userMenuItems", "type", "handleMenuClick", "handleUserMenuClick", "log", "className", "style", "display", "alignItems", "justifyContent", "onClick", "marginRight", "margin", "color", "fontSize", "fontWeight", "menu", "items", "placement", "cursor", "width", "collapsedWidth", "theme", "height", "overflowY", "mode", "<PERSON><PERSON><PERSON><PERSON>", "borderRight", "marginBottom", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/components/Layout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  DashboardOutlined,\n  ProjectOutlined,\n  UserOutlined,\n  BookOutlined,\n  FileTextOutlined,\n  RobotOutlined,\n  SettingOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  LogoutOutlined,\n  BellOutlined,\n  FolderOutlined,\n  DatabaseOutlined,\n  ControlOutlined,\n  EyeOutlined\n} from '@ant-design/icons';\nimport { projectAPI } from '../utils/api';\n\nconst { Header, Sider, Content } = AntLayout;\n\nconst Layout = ({ children }) => {\n  const [collapsed, setCollapsed] = useState(false);\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取当前项目ID（如果在项目页面中）\n  const getProjectId = () => {\n    const pathParts = location.pathname.split('/');\n    if (pathParts[1] === 'projects' && pathParts[2]) {\n      return pathParts[2];\n    }\n    return null;\n  };\n\n  const projectId = getProjectId();\n\n  // 加载项目列表\n  useEffect(() => {\n    loadProjects();\n  }, []);\n\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      const response = await projectAPI.getProjects();\n      const projectsData = response.data.projects || [];\n      setProjects(projectsData);\n    } catch (error) {\n      console.error('加载项目列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 基础菜单项\n  const baseMenuItems = [\n    {\n      key: '/',\n      icon: <DashboardOutlined />,\n      label: '仪表盘',\n    },\n    {\n      key: '/projects',\n      icon: <ProjectOutlined />,\n      label: '项目管理',\n    },\n  ];\n\n  // 项目管理子菜单项\n  const projectSubMenuItems = projectId ? [\n    {\n      key: `/projects/${projectId}/volumes`,\n      icon: <FileTextOutlined />,\n      label: '卷宗管理',\n    },\n    {\n      key: `/projects/${projectId}/content`,\n      icon: <BookOutlined />,\n      label: '内容管理',\n    },\n    {\n      key: `/projects/${projectId}/settings`,\n      icon: <SettingOutlined />,\n      label: '设定管理',\n    },\n  ] : [];\n\n\n\n  // 工具菜单项\n  const toolsMenuItems = [\n    {\n      key: '/ai-assistant',\n      icon: <RobotOutlined />,\n      label: 'Agent-AI助手',\n    },\n    {\n      key: '/ai-test',\n      icon: <RobotOutlined />,\n      label: 'Agent-AI测试',\n    },\n    {\n      key: '/settings',\n      icon: <SettingOutlined />,\n      label: '系统设置',\n    },\n  ];\n\n  // 用户菜单\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '偏好设置',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n    },\n  ];\n\n  const handleMenuClick = ({ key }) => {\n    navigate(key);\n  };\n\n  const handleUserMenuClick = ({ key }) => {\n    switch (key) {\n      case 'profile':\n        navigate('/profile');\n        break;\n      case 'settings':\n        navigate('/settings');\n        break;\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      default:\n        break;\n    }\n  };\n\n  return (\n    <AntLayout className=\"layout-container\">\n      <Header className=\"layout-header\">\n        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <div style={{ display: 'flex', alignItems: 'center' }}>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={() => setCollapsed(!collapsed)}\n              style={{ marginRight: 16 }}\n            />\n            <h1 style={{ margin: 0, color: '#1890ff', fontSize: '20px', fontWeight: 'bold' }}>\n              NovelCraft\n            </h1>\n          </div>\n\n          <Space>\n            <Button type=\"text\" icon={<BellOutlined />} />\n            <Dropdown\n              menu={{\n                items: userMenuItems,\n                onClick: handleUserMenuClick,\n              }}\n              placement=\"bottomRight\"\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <span>用户</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </div>\n      </Header>\n\n      <AntLayout className=\"layout-content\">\n        <Sider\n          className=\"layout-sider\"\n          collapsed={collapsed}\n          width={240}\n          collapsedWidth={80}\n          theme=\"light\"\n        >\n          <div style={{ height: '100%', overflowY: 'auto' }}>\n            {/* 基础菜单 */}\n            <Menu\n              mode=\"inline\"\n              selectedKeys={[location.pathname]}\n              items={baseMenuItems}\n              onClick={handleMenuClick}\n              style={{ borderRight: 0, marginBottom: 0 }}\n            />\n\n            {/* 项目管理子菜单 */}\n            {projectId && (\n              <Menu\n                mode=\"inline\"\n                selectedKeys={[location.pathname]}\n                items={projectSubMenuItems}\n                onClick={handleMenuClick}\n                style={{ borderRight: 0, marginBottom: 0 }}\n              />\n            )}\n\n            {/* 工具菜单 */}\n            <div className=\"tools-divider\">\n              <div className=\"tools-title\">\n                工具\n              </div>\n              <Menu\n                mode=\"inline\"\n                selectedKeys={[location.pathname]}\n                items={toolsMenuItems}\n                onClick={handleMenuClick}\n                style={{ borderRight: 0 }}\n              />\n            </div>\n          </div>\n        </Sider>\n\n        <Content className=\"layout-main\">\n          {children}\n        </Content>\n      </AntLayout>\n    </AntLayout>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,IAAIC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AACjF,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,gBAAgB,EAChBC,eAAe,EACfC,WAAW,QACN,mBAAmB;AAC1B,SAASC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAG5B,SAAS;AAE5C,MAAMD,MAAM,GAAGA,CAAC;EAAE8B;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMwC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMgC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAGF,QAAQ,CAACG,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC;IAC9C,IAAIF,SAAS,CAAC,CAAC,CAAC,KAAK,UAAU,IAAIA,SAAS,CAAC,CAAC,CAAC,EAAE;MAC/C,OAAOA,SAAS,CAAC,CAAC,CAAC;IACrB;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMG,SAAS,GAAGJ,YAAY,CAAC,CAAC;;EAEhC;EACAzC,SAAS,CAAC,MAAM;IACd8C,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BR,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMtB,UAAU,CAACuB,WAAW,CAAC,CAAC;MAC/C,MAAMC,YAAY,GAAGF,QAAQ,CAACG,IAAI,CAACf,QAAQ,IAAI,EAAE;MACjDC,WAAW,CAACa,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMe,aAAa,GAAG,CACpB;IACEC,GAAG,EAAE,GAAG;IACRC,IAAI,eAAE5B,OAAA,CAACjB,iBAAiB;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAE5B,OAAA,CAAChB,eAAe;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMC,mBAAmB,GAAGhB,SAAS,GAAG,CACtC;IACES,GAAG,EAAE,aAAaT,SAAS,UAAU;IACrCU,IAAI,eAAE5B,OAAA,CAACb,gBAAgB;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaT,SAAS,UAAU;IACrCU,IAAI,eAAE5B,OAAA,CAACd,YAAY;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaT,SAAS,WAAW;IACtCU,IAAI,eAAE5B,OAAA,CAACX,eAAe;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,CACF,GAAG,EAAE;;EAIN;EACA,MAAME,cAAc,GAAG,CACrB;IACER,GAAG,EAAE,eAAe;IACpBC,IAAI,eAAE5B,OAAA,CAACZ,aAAa;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAE5B,OAAA,CAACZ,aAAa;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAE5B,OAAA,CAACX,eAAe;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMG,aAAa,GAAG,CACpB;IACET,GAAG,EAAE,SAAS;IACdC,IAAI,eAAE5B,OAAA,CAACf,YAAY;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAE5B,OAAA,CAACX,eAAe;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEI,IAAI,EAAE;EACR,CAAC,EACD;IACEV,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAE5B,OAAA,CAACR,cAAc;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMK,eAAe,GAAGA,CAAC;IAAEX;EAAI,CAAC,KAAK;IACnCf,QAAQ,CAACe,GAAG,CAAC;EACf,CAAC;EAED,MAAMY,mBAAmB,GAAGA,CAAC;IAAEZ;EAAI,CAAC,KAAK;IACvC,QAAQA,GAAG;MACT,KAAK,SAAS;QACZf,QAAQ,CAAC,UAAU,CAAC;QACpB;MACF,KAAK,UAAU;QACbA,QAAQ,CAAC,WAAW,CAAC;QACrB;MACF,KAAK,QAAQ;QACX;QACAa,OAAO,CAACe,GAAG,CAAC,MAAM,CAAC;QACnB;MACF;QACE;IACJ;EACF,CAAC;EAED,oBACExC,OAAA,CAACzB,SAAS;IAACkE,SAAS,EAAC,kBAAkB;IAAArC,QAAA,gBACrCJ,OAAA,CAACC,MAAM;MAACwC,SAAS,EAAC,eAAe;MAAArC,QAAA,eAC/BJ,OAAA;QAAK0C,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAzC,QAAA,gBACrFJ,OAAA;UAAK0C,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAxC,QAAA,gBACpDJ,OAAA,CAACrB,MAAM;YACL0D,IAAI,EAAC,MAAM;YACXT,IAAI,EAAEtB,SAAS,gBAAGN,OAAA,CAACT,kBAAkB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGhC,OAAA,CAACV,gBAAgB;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChEc,OAAO,EAAEA,CAAA,KAAMvC,YAAY,CAAC,CAACD,SAAS,CAAE;YACxCoC,KAAK,EAAE;cAAEK,WAAW,EAAE;YAAG;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACFhC,OAAA;YAAI0C,KAAK,EAAE;cAAEM,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAA/C,QAAA,EAAC;UAElF;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENhC,OAAA,CAACpB,KAAK;UAAAwB,QAAA,gBACJJ,OAAA,CAACrB,MAAM;YAAC0D,IAAI,EAAC,MAAM;YAACT,IAAI,eAAE5B,OAAA,CAACP,YAAY;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9ChC,OAAA,CAACtB,QAAQ;YACP0E,IAAI,EAAE;cACJC,KAAK,EAAEjB,aAAa;cACpBU,OAAO,EAAEP;YACX,CAAE;YACFe,SAAS,EAAC,aAAa;YAAAlD,QAAA,eAEvBJ,OAAA,CAACpB,KAAK;cAAC8D,KAAK,EAAE;gBAAEa,MAAM,EAAE;cAAU,CAAE;cAAAnD,QAAA,gBAClCJ,OAAA,CAACvB,MAAM;gBAACmD,IAAI,eAAE5B,OAAA,CAACf,YAAY;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClChC,OAAA;gBAAAI,QAAA,EAAM;cAAE;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEThC,OAAA,CAACzB,SAAS;MAACkE,SAAS,EAAC,gBAAgB;MAAArC,QAAA,gBACnCJ,OAAA,CAACE,KAAK;QACJuC,SAAS,EAAC,cAAc;QACxBnC,SAAS,EAAEA,SAAU;QACrBkD,KAAK,EAAE,GAAI;QACXC,cAAc,EAAE,EAAG;QACnBC,KAAK,EAAC,OAAO;QAAAtD,QAAA,eAEbJ,OAAA;UAAK0C,KAAK,EAAE;YAAEiB,MAAM,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAxD,QAAA,gBAEhDJ,OAAA,CAACxB,IAAI;YACHqF,IAAI,EAAC,QAAQ;YACbC,YAAY,EAAE,CAACjD,QAAQ,CAACG,QAAQ,CAAE;YAClCqC,KAAK,EAAE3B,aAAc;YACrBoB,OAAO,EAAER,eAAgB;YACzBI,KAAK,EAAE;cAAEqB,WAAW,EAAE,CAAC;cAAEC,YAAY,EAAE;YAAE;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,EAGDd,SAAS,iBACRlB,OAAA,CAACxB,IAAI;YACHqF,IAAI,EAAC,QAAQ;YACbC,YAAY,EAAE,CAACjD,QAAQ,CAACG,QAAQ,CAAE;YAClCqC,KAAK,EAAEnB,mBAAoB;YAC3BY,OAAO,EAAER,eAAgB;YACzBI,KAAK,EAAE;cAAEqB,WAAW,EAAE,CAAC;cAAEC,YAAY,EAAE;YAAE;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CACF,eAGDhC,OAAA;YAAKyC,SAAS,EAAC,eAAe;YAAArC,QAAA,gBAC5BJ,OAAA;cAAKyC,SAAS,EAAC,aAAa;cAAArC,QAAA,EAAC;YAE7B;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhC,OAAA,CAACxB,IAAI;cACHqF,IAAI,EAAC,QAAQ;cACbC,YAAY,EAAE,CAACjD,QAAQ,CAACG,QAAQ,CAAE;cAClCqC,KAAK,EAAElB,cAAe;cACtBW,OAAO,EAAER,eAAgB;cACzBI,KAAK,EAAE;gBAAEqB,WAAW,EAAE;cAAE;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERhC,OAAA,CAACG,OAAO;QAACsC,SAAS,EAAC,aAAa;QAAArC,QAAA,EAC7BA;MAAQ;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAAC3B,EAAA,CA1NI/B,MAAM;EAAA,QAIOO,WAAW,EACXC,WAAW;AAAA;AAAAmF,EAAA,GALxB3F,MAAM;AA4NZ,eAAeA,MAAM;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}