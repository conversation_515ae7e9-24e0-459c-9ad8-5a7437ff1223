{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\ProjectDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Card, Button, Space, Typography, Row, Col, Statistic, Progress, Tag, Descriptions, message, Modal, Form, Input, Select, Spin } from 'antd';\nimport { projectAPI } from '../utils/api';\nimport { EditOutlined, SettingOutlined, ExportOutlined, BackwardOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst ProjectDetail = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [project, setProject] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const loadProject = useCallback(async () => {\n    setLoading(true);\n    try {\n      var _projectData$progress;\n      const response = await axios.get(`/api/projects/${id}`);\n      const projectData = response.data;\n\n      // 格式化项目数据\n      const formattedProject = {\n        ...projectData,\n        createdAt: new Date(projectData.created_at).toLocaleDateString(),\n        updatedAt: new Date(projectData.updated_at).toLocaleDateString(),\n        wordCount: projectData.word_count || 0,\n        chapterCount: projectData.chapter_count || 0,\n        characterCount: projectData.character_count || 0,\n        factionCount: projectData.faction_count || 0,\n        volumeCount: projectData.volume_count || 0,\n        progress: ((_projectData$progress = projectData.progress) === null || _projectData$progress === void 0 ? void 0 : _projectData$progress.completion_percentage) || 0,\n        type: projectData.project_type,\n        status: projectData.status\n      };\n      setProject(formattedProject);\n    } catch (error) {\n      console.error('加载项目详情失败:', error);\n      message.error('加载项目详情失败');\n      navigate('/projects');\n    } finally {\n      setLoading(false);\n    }\n  }, [id, navigate]);\n  useEffect(() => {\n    loadProject();\n  }, [loadProject]);\n\n  // 处理编辑项目\n  const handleEdit = () => {\n    form.setFieldsValue({\n      name: project.name,\n      title: project.title,\n      author: project.author,\n      project_type: project.type,\n      status: project.status,\n      summary: project.summary,\n      description: project.description\n    });\n    setEditModalVisible(true);\n  };\n\n  // 提交编辑\n  const handleEditSubmit = async values => {\n    try {\n      await axios.put(`/api/projects/${id}`, values);\n      message.success('项目更新成功');\n      setEditModalVisible(false);\n      loadProject(); // 重新加载项目数据\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('更新项目失败:', error);\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '更新项目失败');\n    }\n  };\n\n  // 导出项目\n  const handleExport = async () => {\n    try {\n      const response = await axios.post(`/api/projects/${id}/export`, {\n        format: 'json'\n      });\n      message.success('项目导出成功');\n      // 这里可以添加下载逻辑\n    } catch (error) {\n      console.error('导出项目失败:', error);\n      message.error('导出项目失败');\n    }\n  };\n  const getStatusColor = status => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n  const getStatusText = status => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n  const getTypeText = type => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fade-in\",\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u9879\\u76EE\\u8BE6\\u60C5...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this);\n  }\n  if (!project) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fade-in\",\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Text, {\n        children: \"\\u9879\\u76EE\\u4E0D\\u5B58\\u5728\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(BackwardOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate('/projects'),\n          children: \"\\u8FD4\\u56DE\\u9879\\u76EE\\u5217\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          className: \"page-title\",\n          children: project.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: getStatusColor(project.status),\n          children: getStatusText(project.status)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          children: getTypeText(project.type)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        style: {\n          marginTop: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 40\n          }, this),\n          onClick: handleEdit,\n          children: \"\\u7F16\\u8F91\\u9879\\u76EE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 25\n          }, this),\n          onClick: () => navigate(`/projects/${id}/settings`),\n          children: \"\\u9879\\u76EE\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 25\n          }, this),\n          onClick: handleExport,\n          children: \"\\u5BFC\\u51FA\\u9879\\u76EE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5B57\\u6570\",\n            value: project.wordCount,\n            formatter: value => `${(value / 10000).toFixed(1)}万`,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7AE0\\u8282\\u6570\",\n            value: project.chapterCount,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4EBA\\u7269\\u6570\",\n            value: project.characterCount,\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5B8C\\u6210\\u8FDB\\u5EA6\",\n            value: project.progress,\n            suffix: \"%\",\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: project.progress,\n            size: \"small\",\n            status: project.progress === 100 ? 'success' : 'active',\n            style: {\n              marginTop: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u9879\\u76EE\\u6982\\u89C8\",\n      children: /*#__PURE__*/_jsxDEV(Descriptions, {\n        bordered: true,\n        column: 2,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u9879\\u76EE\\u540D\\u79F0\",\n          children: project.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5C0F\\u8BF4\\u6807\\u9898\",\n          children: project.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4F5C\\u8005\",\n          children: project.author\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u9879\\u76EE\\u7C7B\\u578B\",\n          children: getTypeText(project.type)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: project.createdAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u6700\\u540E\\u4FEE\\u6539\",\n          children: project.updatedAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u9879\\u76EE\\u7B80\\u4ECB\",\n          span: 2,\n          children: project.summary\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BE6\\u7EC6\\u63CF\\u8FF0\",\n          span: 2,\n          children: project.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5FEB\\u901F\\u8BBF\\u95EE\",\n      style: {\n        marginTop: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u5377\\u5B97\\u7BA1\\u7406\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              children: \"\\u8FDB\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 22\n            }, this),\n            onClick: () => navigate(`/projects/${id}/volumes`),\n            style: {\n              cursor: 'pointer'\n            },\n            hoverable: true,\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              value: project.volumeCount || 0,\n              suffix: \"\\u4E2A\\u5377\\u5B97\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u7BA1\\u7406\\u5C0F\\u8BF4\\u5377\\u5B97\\u548C\\u7AE0\\u8282\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u5185\\u5BB9\\u7BA1\\u7406\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              children: \"\\u8FDB\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 22\n            }, this),\n            onClick: () => navigate(`/projects/${id}/content`),\n            style: {\n              cursor: 'pointer'\n            },\n            hoverable: true,\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              value: project.characterCount + project.factionCount,\n              suffix: \"\\u4E2A\\u5185\\u5BB9\\u9879\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u7BA1\\u7406\\u89D2\\u8272\\u3001\\u52BF\\u529B\\u3001\\u5267\\u60C5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u8BBE\\u5B9A\\u7BA1\\u7406\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              children: \"\\u8FDB\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 22\n            }, this),\n            onClick: () => navigate(`/projects/${id}/settings`),\n            style: {\n              cursor: 'pointer'\n            },\n            hoverable: true,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u4F53\\u7CFB\\u8BBE\\u5B9A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u7BA1\\u7406\\u4E16\\u754C\\u89C2\\u548C\\u5404\\u7C7B\\u4F53\\u7CFB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"AI\\u52A9\\u624B\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              children: \"\\u4F7F\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 22\n            }, this),\n            onClick: () => navigate('/ai-assistant'),\n            style: {\n              cursor: 'pointer'\n            },\n            hoverable: true,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"AI\\u8F85\\u52A9\\u521B\\u4F5C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u667A\\u80FD\\u751F\\u6210\\u3001\\u7EED\\u5199\\u3001\\u5206\\u6790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u65F6\\u95F4\\u7EBF\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              children: \"\\u67E5\\u770B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 22\n            }, this),\n            onClick: () => navigate(`/projects/${id}/timeline`),\n            style: {\n              cursor: 'pointer'\n            },\n            hoverable: true,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u9879\\u76EE\\u65F6\\u95F4\\u7EBF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u4E8B\\u4EF6\\u3001\\u53D1\\u5C55\\u3001\\u5386\\u53F2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u5173\\u7CFB\\u7F51\\u7EDC\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              children: \"\\u67E5\\u770B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 22\n            }, this),\n            onClick: () => navigate(`/projects/${id}/relations`),\n            style: {\n              cursor: 'pointer'\n            },\n            hoverable: true,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u4EBA\\u7269\\u5173\\u7CFB\\u56FE\\u8C31\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u4EBA\\u7269\\u3001\\u52BF\\u529B\\u3001\\u5173\\u7CFB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u7F16\\u8F91\\u9879\\u76EE\",\n      open: editModalVisible,\n      onCancel: () => setEditModalVisible(false),\n      onOk: () => form.submit(),\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleEditSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u9879\\u76EE\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入项目名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u9879\\u76EE\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u5C0F\\u8BF4\\u6807\\u9898\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5C0F\\u8BF4\\u6807\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"author\",\n              label: \"\\u4F5C\\u8005\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4F5C\\u8005\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"project_type\",\n              label: \"\\u9879\\u76EE\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择项目类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u9879\\u76EE\\u7C7B\\u578B\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"fantasy\",\n                  children: \"\\u5947\\u5E7B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"xianxia\",\n                  children: \"\\u4ED9\\u4FA0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"wuxia\",\n                  children: \"\\u6B66\\u4FA0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"scifi\",\n                  children: \"\\u79D1\\u5E7B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"modern\",\n                  children: \"\\u73B0\\u4EE3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"historical\",\n                  children: \"\\u5386\\u53F2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"romance\",\n                  children: \"\\u8A00\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u9879\\u76EE\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u9879\\u76EE\\u72B6\\u6001\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"planning\",\n                  children: \"\\u89C4\\u5212\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"writing\",\n                  children: \"\\u5199\\u4F5C\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"reviewing\",\n                  children: \"\\u5BA1\\u9605\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"completed\",\n                  children: \"\\u5DF2\\u5B8C\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"published\",\n                  children: \"\\u5DF2\\u53D1\\u5E03\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"summary\",\n          label: \"\\u9879\\u76EE\\u7B80\\u4ECB\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u9879\\u76EE\\u7B80\\u4ECB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u8BE6\\u7EC6\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BE6\\u7EC6\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 5\n  }, this);\n};\n_s(ProjectDetail, \"tJCww8czlWm1dqma+eOGImxAKBM=\", false, function () {\n  return [useParams, useNavigate, Form.useForm];\n});\n_c = ProjectDetail;\nexport default ProjectDetail;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "Card", "<PERSON><PERSON>", "Space", "Typography", "Row", "Col", "Statistic", "Progress", "Tag", "Descriptions", "message", "Modal", "Form", "Input", "Select", "Spin", "projectAPI", "EditOutlined", "SettingOutlined", "ExportOutlined", "BackwardOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "Option", "ProjectDetail", "_s", "id", "navigate", "project", "setProject", "loading", "setLoading", "editModalVisible", "setEditModalVisible", "form", "useForm", "loadProject", "_projectData$progress", "response", "axios", "get", "projectData", "data", "formattedProject", "createdAt", "Date", "created_at", "toLocaleDateString", "updatedAt", "updated_at", "wordCount", "word_count", "chapterCount", "chapter_count", "characterCount", "character_count", "factionCount", "faction_count", "volumeCount", "volume_count", "progress", "completion_percentage", "type", "project_type", "status", "error", "console", "handleEdit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "title", "author", "summary", "description", "handleEditSubmit", "values", "put", "success", "_error$response", "_error$response$data", "detail", "handleExport", "post", "format", "getStatusColor", "colors", "planning", "writing", "reviewing", "completed", "published", "getStatusText", "texts", "getTypeText", "fantasy", "xianxia", "wuxia", "scifi", "modern", "historical", "romance", "className", "style", "textAlign", "padding", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "icon", "onClick", "level", "color", "gutter", "marginBottom", "xs", "sm", "lg", "value", "formatter", "toFixed", "valueStyle", "suffix", "percent", "bordered", "column", "<PERSON><PERSON>", "label", "span", "md", "extra", "cursor", "hoverable", "open", "onCancel", "onOk", "submit", "width", "layout", "onFinish", "rules", "required", "placeholder", "rows", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/ProjectDetail.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Button,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Progress,\n  Tag,\n  Descriptions,\n  message,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Spin\n} from 'antd';\nimport { projectAPI } from '../utils/api';\nimport {\n  EditOutlined,\n  SettingOutlined,\n  ExportOutlined,\n  BackwardOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst ProjectDetail = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const [project, setProject] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [form] = Form.useForm();\n\n  const loadProject = useCallback(async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get(`/api/projects/${id}`);\n      const projectData = response.data;\n\n      // 格式化项目数据\n      const formattedProject = {\n        ...projectData,\n        createdAt: new Date(projectData.created_at).toLocaleDateString(),\n        updatedAt: new Date(projectData.updated_at).toLocaleDateString(),\n        wordCount: projectData.word_count || 0,\n        chapterCount: projectData.chapter_count || 0,\n        characterCount: projectData.character_count || 0,\n        factionCount: projectData.faction_count || 0,\n        volumeCount: projectData.volume_count || 0,\n        progress: projectData.progress?.completion_percentage || 0,\n        type: projectData.project_type,\n        status: projectData.status\n      };\n\n      setProject(formattedProject);\n    } catch (error) {\n      console.error('加载项目详情失败:', error);\n      message.error('加载项目详情失败');\n      navigate('/projects');\n    } finally {\n      setLoading(false);\n    }\n  }, [id, navigate]);\n\n  useEffect(() => {\n    loadProject();\n  }, [loadProject]);\n\n  // 处理编辑项目\n  const handleEdit = () => {\n    form.setFieldsValue({\n      name: project.name,\n      title: project.title,\n      author: project.author,\n      project_type: project.type,\n      status: project.status,\n      summary: project.summary,\n      description: project.description\n    });\n    setEditModalVisible(true);\n  };\n\n  // 提交编辑\n  const handleEditSubmit = async (values) => {\n    try {\n      await axios.put(`/api/projects/${id}`, values);\n      message.success('项目更新成功');\n      setEditModalVisible(false);\n      loadProject(); // 重新加载项目数据\n    } catch (error) {\n      console.error('更新项目失败:', error);\n      message.error(error.response?.data?.detail || '更新项目失败');\n    }\n  };\n\n  // 导出项目\n  const handleExport = async () => {\n    try {\n      const response = await axios.post(`/api/projects/${id}/export`, {\n        format: 'json'\n      });\n      message.success('项目导出成功');\n      // 这里可以添加下载逻辑\n    } catch (error) {\n      console.error('导出项目失败:', error);\n      message.error('导出项目失败');\n    }\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getStatusText = (status) => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n\n  const getTypeText = (type) => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"fade-in\" style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>\n          <Text>正在加载项目详情...</Text>\n        </div>\n      </div>\n    );\n  }\n\n  if (!project) {\n    return (\n      <div className=\"fade-in\" style={{ textAlign: 'center', padding: '50px' }}>\n        <Text>项目不存在</Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Space>\n          <Button\n            icon={<BackwardOutlined />}\n            onClick={() => navigate('/projects')}\n          >\n            返回项目列表\n          </Button>\n          <Title level={2} className=\"page-title\">{project.name}</Title>\n          <Tag color={getStatusColor(project.status)}>\n            {getStatusText(project.status)}\n          </Tag>\n          <Tag>{getTypeText(project.type)}</Tag>\n        </Space>\n\n        <Space style={{ marginTop: 16 }}>\n          <Button type=\"primary\" icon={<EditOutlined />} onClick={handleEdit}>\n            编辑项目\n          </Button>\n          <Button icon={<SettingOutlined />} onClick={() => navigate(`/projects/${id}/settings`)}>\n            项目设置\n          </Button>\n          <Button icon={<ExportOutlined />} onClick={handleExport}>\n            导出项目\n          </Button>\n        </Space>\n      </div>\n\n      {/* 项目统计 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总字数\"\n              value={project.wordCount}\n              formatter={(value) => `${(value / 10000).toFixed(1)}万`}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"章节数\"\n              value={project.chapterCount}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"人物数\"\n              value={project.characterCount}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"完成进度\"\n              value={project.progress}\n              suffix=\"%\"\n              valueStyle={{ color: '#fa8c16' }}\n            />\n            <Progress\n              percent={project.progress}\n              size=\"small\"\n              status={project.progress === 100 ? 'success' : 'active'}\n              style={{ marginTop: 8 }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 项目概览 */}\n      <Card title=\"项目概览\">\n        <Descriptions bordered column={2}>\n          <Descriptions.Item label=\"项目名称\">{project.name}</Descriptions.Item>\n          <Descriptions.Item label=\"小说标题\">{project.title}</Descriptions.Item>\n          <Descriptions.Item label=\"作者\">{project.author}</Descriptions.Item>\n          <Descriptions.Item label=\"项目类型\">{getTypeText(project.type)}</Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">{project.createdAt}</Descriptions.Item>\n          <Descriptions.Item label=\"最后修改\">{project.updatedAt}</Descriptions.Item>\n          <Descriptions.Item label=\"项目简介\" span={2}>\n            {project.summary}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"详细描述\" span={2}>\n            {project.description}\n          </Descriptions.Item>\n        </Descriptions>\n      </Card>\n\n      {/* 快速访问 */}\n      <Card title=\"快速访问\" style={{ marginTop: 24 }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} md={8}>\n            <Card\n              title=\"卷宗管理\"\n              extra={<Button type=\"link\">进入</Button>}\n              onClick={() => navigate(`/projects/${id}/volumes`)}\n              style={{ cursor: 'pointer' }}\n              hoverable\n            >\n              <Statistic value={project.volumeCount || 0} suffix=\"个卷宗\" />\n              <Text type=\"secondary\">管理小说卷宗和章节</Text>\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={8}>\n            <Card\n              title=\"内容管理\"\n              extra={<Button type=\"link\">进入</Button>}\n              onClick={() => navigate(`/projects/${id}/content`)}\n              style={{ cursor: 'pointer' }}\n              hoverable\n            >\n              <Statistic value={project.characterCount + project.factionCount} suffix=\"个内容项\" />\n              <Text type=\"secondary\">管理角色、势力、剧情</Text>\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={8}>\n            <Card\n              title=\"设定管理\"\n              extra={<Button type=\"link\">进入</Button>}\n              onClick={() => navigate(`/projects/${id}/settings`)}\n              style={{ cursor: 'pointer' }}\n              hoverable\n            >\n              <Text>体系设定</Text>\n              <br />\n              <Text type=\"secondary\">管理世界观和各类体系</Text>\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={8}>\n            <Card\n              title=\"AI助手\"\n              extra={<Button type=\"link\">使用</Button>}\n              onClick={() => navigate('/ai-assistant')}\n              style={{ cursor: 'pointer' }}\n              hoverable\n            >\n              <Text>AI辅助创作</Text>\n              <br />\n              <Text type=\"secondary\">智能生成、续写、分析</Text>\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={8}>\n            <Card\n              title=\"时间线\"\n              extra={<Button type=\"link\">查看</Button>}\n              onClick={() => navigate(`/projects/${id}/timeline`)}\n              style={{ cursor: 'pointer' }}\n              hoverable\n            >\n              <Text>项目时间线</Text>\n              <br />\n              <Text type=\"secondary\">事件、发展、历史</Text>\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={8}>\n            <Card\n              title=\"关系网络\"\n              extra={<Button type=\"link\">查看</Button>}\n              onClick={() => navigate(`/projects/${id}/relations`)}\n              style={{ cursor: 'pointer' }}\n              hoverable\n            >\n              <Text>人物关系图谱</Text>\n              <br />\n              <Text type=\"secondary\">人物、势力、关系</Text>\n            </Card>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 编辑项目模态框 */}\n      <Modal\n        title=\"编辑项目\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        onOk={() => form.submit()}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleEditSubmit}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"项目名称\"\n                rules={[{ required: true, message: '请输入项目名称' }]}\n              >\n                <Input placeholder=\"请输入项目名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"title\"\n                label=\"小说标题\"\n              >\n                <Input placeholder=\"请输入小说标题\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"author\"\n                label=\"作者\"\n              >\n                <Input placeholder=\"请输入作者名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"project_type\"\n                label=\"项目类型\"\n                rules={[{ required: true, message: '请选择项目类型' }]}\n              >\n                <Select placeholder=\"请选择项目类型\">\n                  <Option value=\"fantasy\">奇幻</Option>\n                  <Option value=\"xianxia\">仙侠</Option>\n                  <Option value=\"wuxia\">武侠</Option>\n                  <Option value=\"scifi\">科幻</Option>\n                  <Option value=\"modern\">现代</Option>\n                  <Option value=\"historical\">历史</Option>\n                  <Option value=\"romance\">言情</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={24}>\n              <Form.Item\n                name=\"status\"\n                label=\"项目状态\"\n              >\n                <Select placeholder=\"请选择项目状态\">\n                  <Option value=\"planning\">规划中</Option>\n                  <Option value=\"writing\">写作中</Option>\n                  <Option value=\"reviewing\">审阅中</Option>\n                  <Option value=\"completed\">已完成</Option>\n                  <Option value=\"published\">已发布</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"summary\"\n            label=\"项目简介\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"请输入项目简介\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"详细描述\"\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入详细描述\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProjectDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,GAAG,EACHC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,IAAI,QACC,MAAM;AACb,SAASC,UAAU,QAAQ,cAAc;AACzC,SACEC,YAAY,EACZC,eAAe,EACfC,cAAc,EACdC,gBAAgB,QACX,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGrB,UAAU;AAClC,MAAM;EAAEsB;AAAS,CAAC,GAAGZ,KAAK;AAC1B,MAAM;EAAEa;AAAO,CAAC,GAAGZ,MAAM;AAEzB,MAAMa,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAG,CAAC,GAAG/B,SAAS,CAAC,CAAC;EAC1B,MAAMgC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0C,IAAI,CAAC,GAAGzB,IAAI,CAAC0B,OAAO,CAAC,CAAC;EAE7B,MAAMC,WAAW,GAAG1C,WAAW,CAAC,YAAY;IAC1CqC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MAAA,IAAAM,qBAAA;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACC,GAAG,CAAC,iBAAiBd,EAAE,EAAE,CAAC;MACvD,MAAMe,WAAW,GAAGH,QAAQ,CAACI,IAAI;;MAEjC;MACA,MAAMC,gBAAgB,GAAG;QACvB,GAAGF,WAAW;QACdG,SAAS,EAAE,IAAIC,IAAI,CAACJ,WAAW,CAACK,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;QAChEC,SAAS,EAAE,IAAIH,IAAI,CAACJ,WAAW,CAACQ,UAAU,CAAC,CAACF,kBAAkB,CAAC,CAAC;QAChEG,SAAS,EAAET,WAAW,CAACU,UAAU,IAAI,CAAC;QACtCC,YAAY,EAAEX,WAAW,CAACY,aAAa,IAAI,CAAC;QAC5CC,cAAc,EAAEb,WAAW,CAACc,eAAe,IAAI,CAAC;QAChDC,YAAY,EAAEf,WAAW,CAACgB,aAAa,IAAI,CAAC;QAC5CC,WAAW,EAAEjB,WAAW,CAACkB,YAAY,IAAI,CAAC;QAC1CC,QAAQ,EAAE,EAAAvB,qBAAA,GAAAI,WAAW,CAACmB,QAAQ,cAAAvB,qBAAA,uBAApBA,qBAAA,CAAsBwB,qBAAqB,KAAI,CAAC;QAC1DC,IAAI,EAAErB,WAAW,CAACsB,YAAY;QAC9BC,MAAM,EAAEvB,WAAW,CAACuB;MACtB,CAAC;MAEDnC,UAAU,CAACc,gBAAgB,CAAC;IAC9B,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC1D,OAAO,CAAC0D,KAAK,CAAC,UAAU,CAAC;MACzBtC,QAAQ,CAAC,WAAW,CAAC;IACvB,CAAC,SAAS;MACRI,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACL,EAAE,EAAEC,QAAQ,CAAC,CAAC;EAElBlC,SAAS,CAAC,MAAM;IACd2C,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM+B,UAAU,GAAGA,CAAA,KAAM;IACvBjC,IAAI,CAACkC,cAAc,CAAC;MAClBC,IAAI,EAAEzC,OAAO,CAACyC,IAAI;MAClBC,KAAK,EAAE1C,OAAO,CAAC0C,KAAK;MACpBC,MAAM,EAAE3C,OAAO,CAAC2C,MAAM;MACtBR,YAAY,EAAEnC,OAAO,CAACkC,IAAI;MAC1BE,MAAM,EAAEpC,OAAO,CAACoC,MAAM;MACtBQ,OAAO,EAAE5C,OAAO,CAAC4C,OAAO;MACxBC,WAAW,EAAE7C,OAAO,CAAC6C;IACvB,CAAC,CAAC;IACFxC,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMyC,gBAAgB,GAAG,MAAOC,MAAM,IAAK;IACzC,IAAI;MACF,MAAMpC,KAAK,CAACqC,GAAG,CAAC,iBAAiBlD,EAAE,EAAE,EAAEiD,MAAM,CAAC;MAC9CpE,OAAO,CAACsE,OAAO,CAAC,QAAQ,CAAC;MACzB5C,mBAAmB,CAAC,KAAK,CAAC;MAC1BG,WAAW,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,OAAO6B,KAAK,EAAE;MAAA,IAAAa,eAAA,EAAAC,oBAAA;MACdb,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B1D,OAAO,CAAC0D,KAAK,CAAC,EAAAa,eAAA,GAAAb,KAAK,CAAC3B,QAAQ,cAAAwC,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBpC,IAAI,cAAAqC,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,QAAQ,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAM3C,QAAQ,GAAG,MAAMC,KAAK,CAAC2C,IAAI,CAAC,iBAAiBxD,EAAE,SAAS,EAAE;QAC9DyD,MAAM,EAAE;MACV,CAAC,CAAC;MACF5E,OAAO,CAACsE,OAAO,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B1D,OAAO,CAAC0D,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAMmB,cAAc,GAAIpB,MAAM,IAAK;IACjC,MAAMqB,MAAM,GAAG;MACbC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOL,MAAM,CAACrB,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAM2B,aAAa,GAAI3B,MAAM,IAAK;IAChC,MAAM4B,KAAK,GAAG;MACZN,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAAC5B,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAM6B,WAAW,GAAI/B,IAAI,IAAK;IAC5B,MAAM8B,KAAK,GAAG;MACZE,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE;IACX,CAAC;IACD,OAAOR,KAAK,CAAC9B,IAAI,CAAC,IAAIA,IAAI;EAC5B,CAAC;EAED,IAAIhC,OAAO,EAAE;IACX,oBACEX,OAAA;MAAKkF,SAAS,EAAC,SAAS;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,gBACvEtF,OAAA,CAACP,IAAI;QAAC8F,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB3F,OAAA;QAAKmF,KAAK,EAAE;UAAES,SAAS,EAAE;QAAG,CAAE;QAAAN,QAAA,eAC5BtF,OAAA,CAACE,IAAI;UAAAoF,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAClF,OAAO,EAAE;IACZ,oBACET,OAAA;MAAKkF,SAAS,EAAC,SAAS;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,eACvEtF,OAAA,CAACE,IAAI;QAAAoF,QAAA,EAAC;MAAK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAEV;EAEA,oBACE3F,OAAA;IAAKkF,SAAS,EAAC,SAAS;IAAAI,QAAA,gBACtBtF,OAAA;MAAKkF,SAAS,EAAC,aAAa;MAAAI,QAAA,gBAC1BtF,OAAA,CAACpB,KAAK;QAAA0G,QAAA,gBACJtF,OAAA,CAACrB,MAAM;UACLkH,IAAI,eAAE7F,OAAA,CAACF,gBAAgB;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BG,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAAC,WAAW,CAAE;UAAA8E,QAAA,EACtC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3F,OAAA,CAACC,KAAK;UAAC8F,KAAK,EAAE,CAAE;UAACb,SAAS,EAAC,YAAY;UAAAI,QAAA,EAAE7E,OAAO,CAACyC;QAAI;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9D3F,OAAA,CAACd,GAAG;UAAC8G,KAAK,EAAE/B,cAAc,CAACxD,OAAO,CAACoC,MAAM,CAAE;UAAAyC,QAAA,EACxCd,aAAa,CAAC/D,OAAO,CAACoC,MAAM;QAAC;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACN3F,OAAA,CAACd,GAAG;UAAAoG,QAAA,EAAEZ,WAAW,CAACjE,OAAO,CAACkC,IAAI;QAAC;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAER3F,OAAA,CAACpB,KAAK;QAACuG,KAAK,EAAE;UAAES,SAAS,EAAE;QAAG,CAAE;QAAAN,QAAA,gBAC9BtF,OAAA,CAACrB,MAAM;UAACgE,IAAI,EAAC,SAAS;UAACkD,IAAI,eAAE7F,OAAA,CAACL,YAAY;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACG,OAAO,EAAE9C,UAAW;UAAAsC,QAAA,EAAC;QAEpE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3F,OAAA,CAACrB,MAAM;UAACkH,IAAI,eAAE7F,OAAA,CAACJ,eAAe;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACG,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAAC,aAAaD,EAAE,WAAW,CAAE;UAAA+E,QAAA,EAAC;QAExF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3F,OAAA,CAACrB,MAAM;UAACkH,IAAI,eAAE7F,OAAA,CAACH,cAAc;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACG,OAAO,EAAEhC,YAAa;UAAAwB,QAAA,EAAC;QAEzD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN3F,OAAA,CAAClB,GAAG;MAACmH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACd,KAAK,EAAE;QAAEe,YAAY,EAAE;MAAG,CAAE;MAAAZ,QAAA,gBACjDtF,OAAA,CAACjB,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzBtF,OAAA,CAACtB,IAAI;UAAA4G,QAAA,eACHtF,OAAA,CAAChB,SAAS;YACRmE,KAAK,EAAC,oBAAK;YACXmD,KAAK,EAAE7F,OAAO,CAACsB,SAAU;YACzBwE,SAAS,EAAGD,KAAK,IAAK,GAAG,CAACA,KAAK,GAAG,KAAK,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAI;YACvDC,UAAU,EAAE;cAAET,KAAK,EAAE;YAAU;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3F,OAAA,CAACjB,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzBtF,OAAA,CAACtB,IAAI;UAAA4G,QAAA,eACHtF,OAAA,CAAChB,SAAS;YACRmE,KAAK,EAAC,oBAAK;YACXmD,KAAK,EAAE7F,OAAO,CAACwB,YAAa;YAC5BwE,UAAU,EAAE;cAAET,KAAK,EAAE;YAAU;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3F,OAAA,CAACjB,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzBtF,OAAA,CAACtB,IAAI;UAAA4G,QAAA,eACHtF,OAAA,CAAChB,SAAS;YACRmE,KAAK,EAAC,oBAAK;YACXmD,KAAK,EAAE7F,OAAO,CAAC0B,cAAe;YAC9BsE,UAAU,EAAE;cAAET,KAAK,EAAE;YAAU;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3F,OAAA,CAACjB,GAAG;QAACoH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzBtF,OAAA,CAACtB,IAAI;UAAA4G,QAAA,gBACHtF,OAAA,CAAChB,SAAS;YACRmE,KAAK,EAAC,0BAAM;YACZmD,KAAK,EAAE7F,OAAO,CAACgC,QAAS;YACxBiE,MAAM,EAAC,GAAG;YACVD,UAAU,EAAE;cAAET,KAAK,EAAE;YAAU;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACF3F,OAAA,CAACf,QAAQ;YACP0H,OAAO,EAAElG,OAAO,CAACgC,QAAS;YAC1B8C,IAAI,EAAC,OAAO;YACZ1C,MAAM,EAAEpC,OAAO,CAACgC,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG,QAAS;YACxD0C,KAAK,EAAE;cAAES,SAAS,EAAE;YAAE;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3F,OAAA,CAACtB,IAAI;MAACyE,KAAK,EAAC,0BAAM;MAAAmC,QAAA,eAChBtF,OAAA,CAACb,YAAY;QAACyH,QAAQ;QAACC,MAAM,EAAE,CAAE;QAAAvB,QAAA,gBAC/BtF,OAAA,CAACb,YAAY,CAAC2H,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAzB,QAAA,EAAE7E,OAAO,CAACyC;QAAI;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAClE3F,OAAA,CAACb,YAAY,CAAC2H,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAzB,QAAA,EAAE7E,OAAO,CAAC0C;QAAK;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACnE3F,OAAA,CAACb,YAAY,CAAC2H,IAAI;UAACC,KAAK,EAAC,cAAI;UAAAzB,QAAA,EAAE7E,OAAO,CAAC2C;QAAM;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAClE3F,OAAA,CAACb,YAAY,CAAC2H,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAzB,QAAA,EAAEZ,WAAW,CAACjE,OAAO,CAACkC,IAAI;QAAC;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAC/E3F,OAAA,CAACb,YAAY,CAAC2H,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAzB,QAAA,EAAE7E,OAAO,CAACgB;QAAS;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACvE3F,OAAA,CAACb,YAAY,CAAC2H,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAzB,QAAA,EAAE7E,OAAO,CAACoB;QAAS;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACvE3F,OAAA,CAACb,YAAY,CAAC2H,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACC,IAAI,EAAE,CAAE;UAAA1B,QAAA,EACrC7E,OAAO,CAAC4C;QAAO;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACpB3F,OAAA,CAACb,YAAY,CAAC2H,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACC,IAAI,EAAE,CAAE;UAAA1B,QAAA,EACrC7E,OAAO,CAAC6C;QAAW;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eAGP3F,OAAA,CAACtB,IAAI;MAACyE,KAAK,EAAC,0BAAM;MAACgC,KAAK,EAAE;QAAES,SAAS,EAAE;MAAG,CAAE;MAAAN,QAAA,eAC1CtF,OAAA,CAAClB,GAAG;QAACmH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACpBtF,OAAA,CAACjB,GAAG;UAACoH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACa,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACzBtF,OAAA,CAACtB,IAAI;YACHyE,KAAK,EAAC,0BAAM;YACZ+D,KAAK,eAAElH,OAAA,CAACrB,MAAM;cAACgE,IAAI,EAAC,MAAM;cAAA2C,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAE;YACvCG,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAAC,aAAaD,EAAE,UAAU,CAAE;YACnD4E,KAAK,EAAE;cAAEgC,MAAM,EAAE;YAAU,CAAE;YAC7BC,SAAS;YAAA9B,QAAA,gBAETtF,OAAA,CAAChB,SAAS;cAACsH,KAAK,EAAE7F,OAAO,CAAC8B,WAAW,IAAI,CAAE;cAACmE,MAAM,EAAC;YAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3D3F,OAAA,CAACE,IAAI;cAACyC,IAAI,EAAC,WAAW;cAAA2C,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3F,OAAA,CAACjB,GAAG;UAACoH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACa,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACzBtF,OAAA,CAACtB,IAAI;YACHyE,KAAK,EAAC,0BAAM;YACZ+D,KAAK,eAAElH,OAAA,CAACrB,MAAM;cAACgE,IAAI,EAAC,MAAM;cAAA2C,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAE;YACvCG,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAAC,aAAaD,EAAE,UAAU,CAAE;YACnD4E,KAAK,EAAE;cAAEgC,MAAM,EAAE;YAAU,CAAE;YAC7BC,SAAS;YAAA9B,QAAA,gBAETtF,OAAA,CAAChB,SAAS;cAACsH,KAAK,EAAE7F,OAAO,CAAC0B,cAAc,GAAG1B,OAAO,CAAC4B,YAAa;cAACqE,MAAM,EAAC;YAAM;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjF3F,OAAA,CAACE,IAAI;cAACyC,IAAI,EAAC,WAAW;cAAA2C,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3F,OAAA,CAACjB,GAAG;UAACoH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACa,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACzBtF,OAAA,CAACtB,IAAI;YACHyE,KAAK,EAAC,0BAAM;YACZ+D,KAAK,eAAElH,OAAA,CAACrB,MAAM;cAACgE,IAAI,EAAC,MAAM;cAAA2C,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAE;YACvCG,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAAC,aAAaD,EAAE,WAAW,CAAE;YACpD4E,KAAK,EAAE;cAAEgC,MAAM,EAAE;YAAU,CAAE;YAC7BC,SAAS;YAAA9B,QAAA,gBAETtF,OAAA,CAACE,IAAI;cAAAoF,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjB3F,OAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3F,OAAA,CAACE,IAAI;cAACyC,IAAI,EAAC,WAAW;cAAA2C,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3F,OAAA,CAACjB,GAAG;UAACoH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACa,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACzBtF,OAAA,CAACtB,IAAI;YACHyE,KAAK,EAAC,gBAAM;YACZ+D,KAAK,eAAElH,OAAA,CAACrB,MAAM;cAACgE,IAAI,EAAC,MAAM;cAAA2C,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAE;YACvCG,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAAC,eAAe,CAAE;YACzC2E,KAAK,EAAE;cAAEgC,MAAM,EAAE;YAAU,CAAE;YAC7BC,SAAS;YAAA9B,QAAA,gBAETtF,OAAA,CAACE,IAAI;cAAAoF,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnB3F,OAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3F,OAAA,CAACE,IAAI;cAACyC,IAAI,EAAC,WAAW;cAAA2C,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3F,OAAA,CAACjB,GAAG;UAACoH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACa,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACzBtF,OAAA,CAACtB,IAAI;YACHyE,KAAK,EAAC,oBAAK;YACX+D,KAAK,eAAElH,OAAA,CAACrB,MAAM;cAACgE,IAAI,EAAC,MAAM;cAAA2C,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAE;YACvCG,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAAC,aAAaD,EAAE,WAAW,CAAE;YACpD4E,KAAK,EAAE;cAAEgC,MAAM,EAAE;YAAU,CAAE;YAC7BC,SAAS;YAAA9B,QAAA,gBAETtF,OAAA,CAACE,IAAI;cAAAoF,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClB3F,OAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3F,OAAA,CAACE,IAAI;cAACyC,IAAI,EAAC,WAAW;cAAA2C,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3F,OAAA,CAACjB,GAAG;UAACoH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACa,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACzBtF,OAAA,CAACtB,IAAI;YACHyE,KAAK,EAAC,0BAAM;YACZ+D,KAAK,eAAElH,OAAA,CAACrB,MAAM;cAACgE,IAAI,EAAC,MAAM;cAAA2C,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAE;YACvCG,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAAC,aAAaD,EAAE,YAAY,CAAE;YACrD4E,KAAK,EAAE;cAAEgC,MAAM,EAAE;YAAU,CAAE;YAC7BC,SAAS;YAAA9B,QAAA,gBAETtF,OAAA,CAACE,IAAI;cAAAoF,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnB3F,OAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3F,OAAA,CAACE,IAAI;cAACyC,IAAI,EAAC,WAAW;cAAA2C,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP3F,OAAA,CAACX,KAAK;MACJ8D,KAAK,EAAC,0BAAM;MACZkE,IAAI,EAAExG,gBAAiB;MACvByG,QAAQ,EAAEA,CAAA,KAAMxG,mBAAmB,CAAC,KAAK,CAAE;MAC3CyG,IAAI,EAAEA,CAAA,KAAMxG,IAAI,CAACyG,MAAM,CAAC,CAAE;MAC1BC,KAAK,EAAE,GAAI;MAAAnC,QAAA,eAEXtF,OAAA,CAACV,IAAI;QACHyB,IAAI,EAAEA,IAAK;QACX2G,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEpE,gBAAiB;QAAA+B,QAAA,gBAE3BtF,OAAA,CAAClB,GAAG;UAACmH,MAAM,EAAE,EAAG;UAAAX,QAAA,gBACdtF,OAAA,CAACjB,GAAG;YAACiI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZtF,OAAA,CAACV,IAAI,CAACwH,IAAI;cACR5D,IAAI,EAAC,MAAM;cACX6D,KAAK,EAAC,0BAAM;cACZa,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzI,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAkG,QAAA,eAEhDtF,OAAA,CAACT,KAAK;gBAACuI,WAAW,EAAC;cAAS;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN3F,OAAA,CAACjB,GAAG;YAACiI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZtF,OAAA,CAACV,IAAI,CAACwH,IAAI;cACR5D,IAAI,EAAC,OAAO;cACZ6D,KAAK,EAAC,0BAAM;cAAAzB,QAAA,eAEZtF,OAAA,CAACT,KAAK;gBAACuI,WAAW,EAAC;cAAS;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3F,OAAA,CAAClB,GAAG;UAACmH,MAAM,EAAE,EAAG;UAAAX,QAAA,gBACdtF,OAAA,CAACjB,GAAG;YAACiI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZtF,OAAA,CAACV,IAAI,CAACwH,IAAI;cACR5D,IAAI,EAAC,QAAQ;cACb6D,KAAK,EAAC,cAAI;cAAAzB,QAAA,eAEVtF,OAAA,CAACT,KAAK;gBAACuI,WAAW,EAAC;cAAS;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN3F,OAAA,CAACjB,GAAG;YAACiI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZtF,OAAA,CAACV,IAAI,CAACwH,IAAI;cACR5D,IAAI,EAAC,cAAc;cACnB6D,KAAK,EAAC,0BAAM;cACZa,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzI,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAkG,QAAA,eAEhDtF,OAAA,CAACR,MAAM;gBAACsI,WAAW,EAAC,4CAAS;gBAAAxC,QAAA,gBAC3BtF,OAAA,CAACI,MAAM;kBAACkG,KAAK,EAAC,SAAS;kBAAAhB,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC3F,OAAA,CAACI,MAAM;kBAACkG,KAAK,EAAC,SAAS;kBAAAhB,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC3F,OAAA,CAACI,MAAM;kBAACkG,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjC3F,OAAA,CAACI,MAAM;kBAACkG,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjC3F,OAAA,CAACI,MAAM;kBAACkG,KAAK,EAAC,QAAQ;kBAAAhB,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC3F,OAAA,CAACI,MAAM;kBAACkG,KAAK,EAAC,YAAY;kBAAAhB,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC3F,OAAA,CAACI,MAAM;kBAACkG,KAAK,EAAC,SAAS;kBAAAhB,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3F,OAAA,CAAClB,GAAG;UAACmH,MAAM,EAAE,EAAG;UAAAX,QAAA,eACdtF,OAAA,CAACjB,GAAG;YAACiI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZtF,OAAA,CAACV,IAAI,CAACwH,IAAI;cACR5D,IAAI,EAAC,QAAQ;cACb6D,KAAK,EAAC,0BAAM;cAAAzB,QAAA,eAEZtF,OAAA,CAACR,MAAM;gBAACsI,WAAW,EAAC,4CAAS;gBAAAxC,QAAA,gBAC3BtF,OAAA,CAACI,MAAM;kBAACkG,KAAK,EAAC,UAAU;kBAAAhB,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrC3F,OAAA,CAACI,MAAM;kBAACkG,KAAK,EAAC,SAAS;kBAAAhB,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC3F,OAAA,CAACI,MAAM;kBAACkG,KAAK,EAAC,WAAW;kBAAAhB,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC3F,OAAA,CAACI,MAAM;kBAACkG,KAAK,EAAC,WAAW;kBAAAhB,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC3F,OAAA,CAACI,MAAM;kBAACkG,KAAK,EAAC,WAAW;kBAAAhB,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3F,OAAA,CAACV,IAAI,CAACwH,IAAI;UACR5D,IAAI,EAAC,SAAS;UACd6D,KAAK,EAAC,0BAAM;UAAAzB,QAAA,eAEZtF,OAAA,CAACG,QAAQ;YACP4H,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAS;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ3F,OAAA,CAACV,IAAI,CAACwH,IAAI;UACR5D,IAAI,EAAC,aAAa;UAClB6D,KAAK,EAAC,0BAAM;UAAAzB,QAAA,eAEZtF,OAAA,CAACG,QAAQ;YACP4H,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAS;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACrF,EAAA,CAjaID,aAAa;EAAA,QACF7B,SAAS,EACPC,WAAW,EAIba,IAAI,CAAC0B,OAAO;AAAA;AAAAgH,EAAA,GANvB3H,aAAa;AAmanB,eAAeA,aAAa;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}